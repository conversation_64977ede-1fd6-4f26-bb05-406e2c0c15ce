using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Reporting.Helper;
using Sacrra.Membership.Reporting.Models.ReportCommon;
using System.Collections.Generic;
using System.Linq;
using Sacrra.Membership.Business.DTOs.DataWarehouseDTO;
using Sacrra.Membership.Business.Services.DataWarehouseService;
using System;

namespace Sacrra.Membership.Reporting.Services
{
    public class ReportCommonService
    {
        private AppDbContext _dbContext;
        private DataWarehouseService _dataWarehouseService;
        private ReportTables _reportTables;
        public IMapper _mapper { get; }

        public ReportCommonService(AppDbContext dbContext, DataWarehouseService dataWarehouseService, IMapper mapper)
        {
            _dbContext = dbContext;
            _dataWarehouseService = dataWarehouseService;
            _reportTables = _dataWarehouseService.ReportTables;
            _mapper = mapper;
        }

        public Level1Filter[] GetLevel1Filters(string auth0UserName)
        {
            var user = _dbContext.Users.FirstOrDefault(u => u.Auth0Id == auth0UserName);
            if (user == null)
                return new List<Level1Filter>().ToArray();
            var userRole = user.RoleId;

            // If I'm logging in as a user that has access to all the members (backoffice) I get all the members available from the DW
            if (userRole == UserRoles.FinancialAdministrator
                || userRole == UserRoles.SACRRAAdministrator
                || userRole == UserRoles.StakeHolderAdministrator
                || userRole == UserRoles.StakeHolderManager
                || userRole == UserRoles.Bureau)
            {
                var apiCallModel = new DataWarehouseAPIModel()
                {
                    Columns = "MemberName, AlgName",
                    OrderBy = "MemberName"
                };
                var results = _dataWarehouseService.GetResultArray<Level1Filter>(_reportTables.TopLevelFilterView, apiCallModel);

                return results;
            }
            // If I'm logged in as a member I can only see the data for that member
            else if (userRole == UserRoles.Member)
            {
                var availableMembersList = _dbContext.Members
                    .Include(m => m.Users)
                    .Where(m => m.Users.Any(i => i.UserId == user.Id) && (m.MembershipTypeId == MembershipTypes.FullMember 
                        || m.MembershipTypeId == MembershipTypes.NonMember))
                    .Select(m => new Level1Filter() {
                        AlgName = "",
                        MemberName = m.RegisteredName
                    }).OrderBy(r => r.MemberName).ToArray();
                return availableMembersList;
            }
            else if (userRole == UserRoles.ALGLeader)
            {
                // Get the ALG Leader's member name
                var algLeaderMemberName = _dbContext.MemberUsers
                    .Include(x => x.Member)
                    .Where(x => x.UserId == user.Id && x.Member.MembershipTypeId == MembershipTypes.ALGLeader)
                    .Select(x => x.Member.RegisteredName)
                    .ToList()
                    .FirstOrDefault();

                var availableMembersList = _dbContext.Members
                    .Include(m => m.Users)
                    .Where(m => m.Users.Any(i => i.UserId == user.Id) && (m.MembershipTypeId == MembershipTypes.ALGLeader
                        || m.MembershipTypeId == MembershipTypes.ALGClient))
                    .Select(m => new Level1Filter()
                    {
                        AlgName = algLeaderMemberName,
                        MemberName = m.RegisteredName
                    }).OrderBy(r => r.MemberName).ToArray();
                return availableMembersList;
            }
            else
            {
                return new List<Level1Filter>().ToArray();
            }
        }

        private string BuildWhereClause(string[] members = null, string[] spNumbers = null, string[] algs = null, string[] creditInformationClassification = null)
        {
            var conditions = new List<string>();

            if (members?.Any() == true)
                conditions.Add($"MemberName IN ({string.Join(",", members.Select(m => $"'{m.Replace("'", "''")}'"))})");
            
            if (spNumbers?.Any() == true)
                conditions.Add($"SPNumber IN ({string.Join(",", spNumbers.Select(m => $"'{m.Replace("'", "''")}'"))})");
            
            if (algs?.Any() == true)
                conditions.Add($"AlgName IN ({string.Join(",", algs.Select(m => $"'{m.Replace("'", "''")}'"))})");
            
            if (creditInformationClassification?.Any() == true && !string.IsNullOrWhiteSpace(creditInformationClassification[0]))
                conditions.Add($"CreditInformationClassification IN ({string.Join(",", creditInformationClassification.Select(m => $"'{m.Replace("'", "''")}'"))})");

            return string.Join(" AND ", conditions);
        }

        public Level2Filter[] GetLevel2Filters(RejectionReportParameters parameters)
        {
            var whereClause = BuildWhereClause(
                members: parameters.MemberName,
                algs: parameters.Algs,
                creditInformationClassification: parameters.CreditInformationClassification);

            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "SRNNumber, SPNumber, CreditInformationClassification",
                OrderBy = "SRNNumber, SPNumber, CreditInformationClassification",
                GroupBy = "SRNNumber, SPNumber, CreditInformationClassification",
                Where = whereClause
            };
            var results = _dataWarehouseService.GetResultArray<Level2Filter>(_reportTables.TopLevelFilterView, apiCallModel);
            return results;
        }

        public Level2Filter[] GetLevel2FiltersDb(RejectionReportParameters parameters)
        {
            var query = _dbContext.SRNs
                .Include(s => s.Member)
                .Include(s => s.CreditInformationClassification)
                .Include(s => s.ALGLeader)
                .Include(s => s.SPGroup)
                .AsQueryable();

            // Filter by Member Names if provided
            if (parameters.MemberName != null && parameters.MemberName.Any())
            {
                query = query.Where(s => parameters.MemberName.Contains(s.Member.RegisteredName));
            }

            // Filter by ALG Names if provided
            if (parameters.Algs != null && parameters.Algs.Any())
            {
                query = query.Where(s => s.ALGLeader != null && parameters.Algs.Contains(s.ALGLeader.RegisteredName));
            }

            // Filter by Credit Information Classification if provided
            if (parameters.CreditInformationClassification != null && parameters.CreditInformationClassification.Any() 
                && !string.IsNullOrWhiteSpace(parameters.CreditInformationClassification[0]))
            {
                query = query.Where(s => s.CreditInformationClassification != null && 
                    parameters.CreditInformationClassification.Contains(s.CreditInformationClassification.Name));
            }

            // Select and transform to Level2Filter
            var results = query
                .Select(s => new Level2Filter
                {
                    SRNNumber = s.SRNNumber ?? string.Empty,
                    SPNumber = s.SPGroup.SPNumber ?? string.Empty,
                    CreditInformationClassification = s.CreditInformationClassification.Name ?? string.Empty
                })
                .Where(s => s.SRNNumber != null)
                .OrderBy(s => s.SRNNumber)
                .ThenBy(s => s.CreditInformationClassification)
                .Distinct()
                .ToArray();

            return results;
        }

        public string[] GetBureauList()
        {
            return GetBureauObscureMappings()
                .Select(m => m.RegisteredName)
                .ToArray();
        }

        private List<BureauObscureMappingOutputDTO> GetBureauObscureMappings()
        {
            var bureaus = _dbContext.BureauObscureMappings
                    .Include(i => i.Member)
                    .Select(m => new BureauObscureMapping
                    {
                        Id = m.Id,
                        ObscureName = m.ObscureName,
                        Member = new()
                        {
                            RegisteredName = m.Member.RegisteredName
                        }
                    })
                    .AsQueryable();

            var obsureMappings = _mapper.Map<List<BureauObscureMappingOutputDTO>>(bureaus.ToList());

            return obsureMappings;
        }

        public LoadStats GetLoadStats()
        {
            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "LoadStatID, ETLFrameworkRefresh, WarehouseRefresh"
            };

            var results = _dataWarehouseService.GetResultArray<LoadStats>(_reportTables.LoadStatsView, apiCallModel);
            if (results != null)
            {
                if (results.Length > 0)
                {
                    return results[0];
                }
            }

            return null;
        }

        public Level1Filter[] GetLevel1FiltersDb(string auth0UserName)
        {
            var user = _dbContext.Users.FirstOrDefault(u => u.Auth0Id == auth0UserName);
            if (user == null)
                return new List<Level1Filter>().ToArray();

            var userRole = user.RoleId;

            // Back office users get all members
            if (userRole == UserRoles.StakeHolderManager
                || userRole == UserRoles.FinancialAdministrator
                || userRole == UserRoles.SACRRAAdministrator
                || userRole == UserRoles.StakeHolderAdministrator)
            {
                var results = _dbContext.Members
                    .Include(m => m.Leaders)
                        .ThenInclude(a => a.Leader)
                    .Select(m => new Level1Filter()
                    {
                        AlgName = m.Leaders
                            .Select(a => a.Leader.RegisteredName)
                            .FirstOrDefault() ?? "",
                        MemberName = m.RegisteredName
                    })
                    .OrderBy(r => r.MemberName)
                    .ToArray();

                return results;
            }
            
            // Member role
            if (userRole == UserRoles.Member)
            {
                var availableMembersList = _dbContext.Members
                    .Include(m => m.Users)
                    .Where(m => m.Users.Any(i => i.UserId == user.Id) && (m.MembershipTypeId == MembershipTypes.FullMember
                        || m.MembershipTypeId == MembershipTypes.NonMember))
                    .Select(m => new Level1Filter()
                    {
                        AlgName = "",
                        MemberName = m.RegisteredName
                    }).OrderBy(r => r.MemberName).ToArray();
                return availableMembersList;
            }
            
            // ALG Leader role
            if (userRole == UserRoles.ALGLeader)
            {
                var algLeaderMemberName = _dbContext.MemberUsers
                    .Include(x => x.Member)
                    .Where(x => x.UserId == user.Id && x.Member.MembershipTypeId == MembershipTypes.ALGLeader)
                    .Select(x => x.Member.RegisteredName)
                    .ToList()
                    .FirstOrDefault();

                var availableMembersList = _dbContext.Members
                    .Include(m => m.Users)
                    .Where(m => m.Users.Any(i => i.UserId == user.Id) && (m.MembershipTypeId == MembershipTypes.ALGLeader
                        || m.MembershipTypeId == MembershipTypes.ALGClient))
                    .Select(m => new Level1Filter()
                    {
                        AlgName = algLeaderMemberName,
                        MemberName = m.RegisteredName
                    }).OrderBy(r => r.MemberName).ToArray();
                return availableMembersList;
            }
            
            // Bureau role
            if (userRole == UserRoles.Bureau)
            {
                var availableMembersList = _dbContext.Members
                    .Include(m => m.Leaders)
                    .ThenInclude(a => a.Leader)
                    .Select(m => new Level1Filter()
                    {
                        AlgName = m.Leaders
                            .Select(a => a.Leader.RegisteredName)
                            .FirstOrDefault() ?? "",
                        MemberName = m.RegisteredName
                    })
                    .OrderBy(r => r.MemberName)
                    .ToArray();
                return availableMembersList;
            }

            return new List<Level1Filter>().ToArray();
        }

        public string[] GetFilteredSRNs(RejectionReportParameters parameters)
        {
            var query = _dbContext.SRNs
                .Include(s => s.Member)
                .Include(s => s.Member.PrimaryBureau)
                .Include(s => s.Member.SecondaryBureau)
                .Include(s => s.ALGLeader)
                .AsQueryable();
            if (parameters.MemberName?.Length > 0)
            {
                query = query.Where(s => parameters.MemberName.Contains(s.Member.RegisteredName));
            }
            if (parameters.BureauName?.Length > 0)
            {
                query = query.Where(s =>
                    (s.Member.PrimaryBureau != null && parameters.BureauName.Contains(s.Member.PrimaryBureau.RegisteredName)) ||
                    (s.Member.SecondaryBureau != null && parameters.BureauName.Contains(s.Member.SecondaryBureau.RegisteredName))
                );
            }
            if (parameters.Algs?.Length > 0)
            {
                query = query.Where(s =>
                    s.ALGLeaderId.HasValue &&
                    parameters.Algs.Contains(s.ALGLeader.RegisteredName));
            }
            return query.Select(s => s.SRNNumber)
                .Distinct()
                .ToArray();
        }

        public string[] GetFilteredSRNs(IndustryBenchmarkInputReportInputFilterDTO parameters)
        {
            var query = _dbContext.SRNs
                .Include(s => s.Member)
                .Include(s => s.Member.PrimaryBureau)
                .Include(s => s.Member.SecondaryBureau)
                .Include(s => s.ALGLeader)
                .AsQueryable();
            if (!string.IsNullOrEmpty(parameters.Member))
            {
                query = query.Where(s => s.Member.RegisteredName == parameters.Member);
            }
            if (parameters.Bureau?.Length > 0)
            {
                query = query.Where(s => 
                    (s.Member.PrimaryBureau != null && parameters.Bureau.Contains(s.Member.PrimaryBureau.RegisteredName)) ||
                    (s.Member.SecondaryBureau != null && parameters.Bureau.Contains(s.Member.SecondaryBureau.RegisteredName))
                );
            }
            if (!string.IsNullOrEmpty(parameters.ALGLeader))
            {
                query = query.Where(s => 
                    s.ALGLeaderId.HasValue && 
                    s.ALGLeader.RegisteredName == parameters.ALGLeader);
            }
            return query.Select(s => s.SRNNumber)
                .Distinct()
                .ToArray();
        }

        public DropdownFilterResponse<string> GetFilteredMembers(MembersFilterRequest request, string auth0UserName)
        {
            var user = _dbContext.Users.FirstOrDefault(u => u.Auth0Id == auth0UserName);
            if (user == null)
                return new DropdownFilterResponse<string>();
            var filteredMembers = new List<string>();
            // Handle different user roles
            if (user.RoleId == UserRoles.FinancialAdministrator
                || user.RoleId == UserRoles.SACRRAAdministrator
                || user.RoleId == UserRoles.StakeHolderAdministrator
                || user.RoleId == UserRoles.StakeHolderManager
                || user.RoleId == UserRoles.Bureau)
            {
                // Get ALG clients if ALGs are specified
                if (request.ALGs != null && request.ALGs.Any())
                {
                    filteredMembers = _dbContext.Members
                        .Include(m => m.Leaders)
                        .Where(m => m.Leaders.Any(l => request.ALGs.Contains(l.Leader.RegisteredName))
                            && m.MembershipTypeId == MembershipTypes.ALGClient)
                        .Select(m => m.RegisteredName)
                        .ToList();
                }else{
                    filteredMembers = _dbContext.Members
                        .Select(m => m.RegisteredName)
                        .ToList ();
                }
            }
            else if (user.RoleId == UserRoles.Member)
            {
                filteredMembers = _dbContext.Members
                    .Include(m => m.Users)
                    .Where(m => m.Users.Any(i => i.UserId == user.Id)
                        && (m.MembershipTypeId == MembershipTypes.FullMember
                            || m.MembershipTypeId == MembershipTypes.NonMember))
                    .Select(m => m.RegisteredName)
                    .ToList();
            }
            else if (user.RoleId == UserRoles.ALGLeader)
            {
                filteredMembers = _dbContext.Members
                    .Include(m => m.Users)
                    .Where(m => m.Users.Any(i => i.UserId == user.Id)
                        && (m.MembershipTypeId == MembershipTypes.ALGLeader
                            || m.MembershipTypeId == MembershipTypes.ALGClient))
                    .Select(m => m.RegisteredName)
                    .ToList();
            }
            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                filteredMembers = filteredMembers.Where(m => m.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase)).ToList();
            }
            var totalCount = filteredMembers.Count;
            var items = filteredMembers
                .OrderBy(m => m)
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();
            return new DropdownFilterResponse<string>
            {
                Items = items,
                TotalCount = totalCount,
                HasMore = totalCount > (request.PageNumber * request.PageSize)
            };
        }

        public DropdownFilterResponse<string> GetFilteredSrnsDropdown(SRNFilterRequest request)
        {
            var whereClause = BuildWhereClause(
                members: request.Members?.ToArray(),
                spNumbers: request.SPNumbers?.ToArray(),
                algs: request.Algs?.ToArray(),
                creditInformationClassification: request.CreditInformationClassification?.ToArray());
            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "SRNNumber, SPNumber, CreditInformationClassification",
                OrderBy = "SRNNumber, SPNumber, CreditInformationClassification",
                GroupBy = "SRNNumber, SPNumber, CreditInformationClassification",
                Where = whereClause
            };
            var results = _dataWarehouseService.GetResultArray<Level2Filter>(_reportTables.TopLevelFilterView, apiCallModel);
            var srns = results.Select(r => r.SRNNumber).ToList();
            var totalCount = srns.Count;
            var paginatedSrns = srns
                .OrderBy(srn => srn)
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();
            return new DropdownFilterResponse<string>
            {
                Items = paginatedSrns,
                TotalCount = totalCount,
                HasMore = totalCount > (request.PageNumber * request.PageSize)
            };
        }

        public DropdownFilterResponse<string> GetFilteredDropdownItems(DropdownFilterRequest request, string auth0UserName)
        {
            switch (request.TargetField.ToLower())
            {
                case "member":
                    return GetFilteredMembers(new MembersFilterRequest
                    {
                        ALGs = request.Filters.GetValueOrDefault("algs", new string[] { }).ToList(),
                        SearchTerm = request.SearchTerm,
                        PageSize = request.PageSize,
                        PageNumber = request.PageNumber
                    }, auth0UserName);
                case "srnnumber":
                    return GetFilteredSrnsDropdown(new SRNFilterRequest
                    {
                        SPNumbers = request.Filters?.GetValueOrDefault("spNumbers", Array.Empty<string>())?.ToList() ?? new List<string>(),
                        Algs = request.Filters?.GetValueOrDefault("algs", Array.Empty<string>())?.ToList() ?? new List<string>(),
                        CreditInformationClassification = request.Filters?.GetValueOrDefault("creditInformationClassification", Array.Empty<string>())?.ToList() ?? new List<string>(),
                        Members = request.Filters?.GetValueOrDefault("members", Array.Empty<string>())?.ToList() ?? new List<string>(),
                        SearchTerm = request.SearchTerm,
                        PageSize = request.PageSize,
                        PageNumber = request.PageNumber
                    });
                default:
                    throw new Exception($"Unsupported target field: {request.TargetField}");
            }
        }
    }
}
