using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Database;
using Serilog;
using Ssi.Scf.Database;
using System;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using Microsoft.Extensions.Hosting;
using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var host = CreateWebHostBuilder(args).Build();

            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(Configuration)
                .CreateLogger();

            //If we have problems getting the log to write to SQL Server, we use self logging
            //to surface errors from Serilog
            Serilog.Debugging.SelfLog.Enable(msg =>
            {
                Debug.Print(msg);
            });

            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                var context = services.GetRequiredService<AppDbContext>();
                var authRepository = services.GetRequiredService<AuthRepository>();
                
                context.Database.Migrate();
                SeedData.Initialize(services);

                bool canCreateUsers = Convert.ToBoolean(Configuration["ConfigSettings:CreateInternalUsersInAuth0AtStartup"]);
                if (canCreateUsers)
                {
                    authRepository.CreateInternalUsersInAuth0();
                }
                
            }

            host.Run();
        }

        public static IHostBuilder CreateWebHostBuilder(string[] args)
        {
            return Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webHostBuilder => webHostBuilder.ConfigureAppConfiguration((hostingContext, config) =>
                {
                    config.AddJsonFile($"{HostingPath}appsettings.json", optional: false, reloadOnChange: true);
                    config.AddJsonFile($"{HostingPath}appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true);
                    ConfigureAzureVault(config);
                })
                .UseStartup<Startup>())
                .UseDefaultServiceProvider(options => options.ValidateScopes = false) 
                .UseSerilog();
        }

        private static void ConfigureAzureVault(IConfigurationBuilder config)
        {
            var configBuild = config.Build();
            var vaultName = configBuild["KeyVault:Vault"];
            var clientId = configBuild["KeyVault:ClientId"];
            var tenantId = configBuild["KeyVault:TenantId"];
            var clientSecret = Environment.GetEnvironmentVariable("KeyVaultClientSecret") ?? configBuild["KeyVaultClientSecret"];

            if (!string.IsNullOrWhiteSpace(vaultName))
            {
                var tokenCredential = new ClientSecretCredential(
                    tenantId,
                    clientId,
                    clientSecret
                );
                var secretClient = new SecretClient(
                    new Uri($"https://{vaultName}.vault.azure.net/"),
                    tokenCredential
                );
                config.AddAzureKeyVault(secretClient, new KeyVaultSecretManager());
            }
        }

        public static IConfiguration Configuration { get; } = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile($"{HostingPath}appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"{HostingPath}appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production"}.json", optional: true)
            .Build();

        private static string HostingPath
        {
            get
            {
                var path = Directory.GetCurrentDirectory();

                //This is to fix the back slashes as they are not working in docker containers
                if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") != "LocalDevelopment")
                {
                    path = path + "/AppSettings/";
                }
                else
                {
                    path = string.Concat(path + "/AppSettings/");
                }

                return path;
            }
        }
    }
}
