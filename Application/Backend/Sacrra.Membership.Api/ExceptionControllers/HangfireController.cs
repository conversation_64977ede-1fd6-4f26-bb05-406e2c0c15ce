using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Exceptions.Services;
using System.Threading.Tasks;

namespace Sacrra.Membership.Api.ExceptionControllers
{
    [Authorize(Roles = "System Administrator")]
    [ApiController]
    [ApiVersion("2")]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    public class HangfireController : Controller
    {
        private HangfireService _hangfireService;

        //TODO: The services need to be combined
        private Sacrra.Membership.Business.Services.HangfireService _hangfireServiceHelper;

        public HangfireController(HangfireService hangfireService, Sacrra.Membership.Business.Services.HangfireService hangfireServiceHelper)
        {
            _hangfireService = hangfireService;
            _hangfireServiceHelper = hangfireServiceHelper;
        }

        [HttpPut("fetch-dw-exceptions")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> FetchDWExceptions()
        {
            await _hangfireService.CreateCamundaTaskForNewDWExceptions();
            return Ok();
        }
    }
}
