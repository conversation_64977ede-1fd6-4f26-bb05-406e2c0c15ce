using Microsoft.AspNetCore.Mvc;
using Saccra.Membership.Business.Repositories;
using Sacrra.Membership.Business.DTOs.SRNUpdateDTOs;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Resources.AccountType;
using Sacrra.Membership.Business.Services;
using System.Threading.Tasks;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Business.Resources.IdValuePair;
using System.Collections.Generic;

namespace Sacrra.Membership.Api.ControllersV2
{
    [ApiController]
    [ApiVersion("2")]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    public class DisqualifiedReasonController : Controller
    {
        private readonly DisqualifiedReasonTypesRepository _repository;

        public DisqualifiedReasonController(DisqualifiedReasonTypesRepository disqualifiedReasonTypesRepository)
        {
            _repository = disqualifiedReasonTypesRepository;
        }

      
        [HttpGet( Name = "GetAllReasons")]
        [ProducesResponseType(typeof(List<IdValuePairResource>), 200)]
        [ProducesResponseType(typeof(void), 404)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetAll()
        {
            var entity = await _repository.GetAllDisqualifiedReasons();
            if (entity == null)
                return NotFound();

            return Ok(entity);
        }

        
        [HttpGet("{id}", Name = "GetReasonType")]
        [ProducesResponseType(typeof(DisqualifiedReason), 200)]
        [ProducesResponseType(typeof(void), 404)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> Get(int id)
        {
            var entity = await _repository.GetById(id);
            if (entity == null)
                return NotFound();

            return Ok(entity);
        }

        [HttpGet("{id}", Name = "GeMappingtById")]
        [ProducesResponseType(typeof(DisqualifiedReasonMapping), 200)]
        [ProducesResponseType(typeof(void), 404)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GeMappingtById(int id)
        {
            var entity = await _repository.GeMappingtById(id);
            if (entity == null)
                return NotFound();

            return Ok(entity);
        }


        [HttpPut]
        [ProducesResponseType(typeof(DisqualifiedReasonMapping), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> UpdateMapping([FromBody] DisqualifiedReasonMapping srnUpdateInputDTO)
        {
            
            return Ok(await _repository.UpdateMappingAsync(srnUpdateInputDTO));
        }

    }
}
