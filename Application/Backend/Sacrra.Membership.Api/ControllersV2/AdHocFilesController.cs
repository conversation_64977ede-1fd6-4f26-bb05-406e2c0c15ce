using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RestSharp;
using Sacrra.Membership.Business.DTOs.AdhocFileSubmissionDTOs;
using Sacrra.Membership.Business.DTOs.ReplacementFileSubmissionDTOs;
using Sacrra.Membership.Business.Services.AdHocFilesService;
using Sacrra.Membership.Business.Services.ReplacementFileSubmissionService;
using System.Data;

namespace Sacrra.Membership.Api.ControllersV2
{

    [Authorize(Roles = "Member, ALG Leader, Stakeholder Manager, Bureau")]
    [ApiController]
    [ApiVersion("2")]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]

    public class AdhocFilesController: Controller
    {

        private AdhocFilesService _adhocFilsService;

        public AdhocFilesController( AdhocFilesService adhocFilesService)
        {
            _adhocFilsService = adhocFilesService;
        }

        [HttpPost]
        [ProducesResponseType(typeof(RestResponse), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public RestResponse RequestAdhocFileSubmission([FromBody] AdhocFileSubmissionInputDTO inputDTO)
        {
            return _adhocFilsService.RequestAdhocFileSubmission(inputDTO);
        }
    }
}
