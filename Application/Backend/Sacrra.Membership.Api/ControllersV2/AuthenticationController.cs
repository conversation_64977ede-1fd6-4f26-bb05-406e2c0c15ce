using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.DTOs.AuthDTOs;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Business.Exceptions;

namespace Sacrra.Membership.Api.ControllersV2
{
    [ApiController]
    [ApiVersion("2")]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    public class AuthenticationController : Controller
    {
        private readonly AuthenticationService _authService;

        public AuthenticationController(AuthenticationService authService)
        {
            _authService = authService;
        }

        [HttpPost]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult AddAuth0User([FromBody] AuthInputDTO authInputDTO)
        {
            if (!ModelState.IsValid)
                throw new InvalidUserException();

            _authService.SaveAuth0User(authInputDTO);

            return Ok();
        }

        [HttpPost]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult ResendEmailVerification([FromBody] EmailVerificationInputDTO emailVerificationInputDTO)
        {
            _authService.ResendEmailVerification(emailVerificationInputDTO);

            return Ok();
        }

        [HttpPost]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult SendPasswordResetEmail([FromBody] AuthPasswordResetInputDTO passwordResetDTO)
        {
            var result = _authService.SendPasswordResetEmail(passwordResetDTO);

            return Ok(result);
        }

        [Authorize(Roles = "SACRRA Administrator,System Administrator")]
        [HttpPut]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult UpdateAuth0LoginTemplate()
        {
            var response = _authService.UpdateAuth0LoginTemplate();
            if(response.IsSuccessful)
                return Ok();

            return BadRequest(response);
        }
    }
}
