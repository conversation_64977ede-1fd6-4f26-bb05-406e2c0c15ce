using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.DTOs.TaskDTOs;
using Sacrra.Membership.Business.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sacrra.Membership.Business.DTOs.SRNHasTaskDTOs;
using Sacrra.Membership.Business.DTOs.ReplacementFileSubmissionDTOs;
using RestSharp;
using Sacrra.Membership.Business.DTOs.AdHocFIlesDTO;
using Sacrra.Membership.Camunda.CamundaDTOs;
using Sacrra.Membership.Camunda.UserServices;

namespace Sacrra.Membership.Api.ControllersV2
{
    [ApiController]
    [ApiVersion("2")]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    public class TasksController : Controller
    {
        private readonly CamundaService _camundaService;
        private const string MEMBER_TAKEON_PROCESS_KEY = "New-Member-Takeon";
        private const string SRN_APPLICATION_PROCESS_KEY = "New-SRN-Application";
        private const string MEMBER_UPDATE_PROCESS_KEY = "Member-Update-Details";
        private const string SRN_UPDATE_PROCESS_KEY = "SRN-Update-Details";
        private const string SRN_MERGE_SPLIT_SALE_PROCESS_KEY = "SRN-Split-Merge-Sell";
        private const string MEMBER_STATUS_UPDATE = "Member-Status-Update";
        private const string SRN_STATUS_UPDATE = "SRN-Status-Update";
        private const string SRN_STATUS_UPDATE_NON_CANCELLATIONS = "SRN-Status-Update-Non-Cancellations";
        private const string SRN_STATUS_UPDATE_TO_TEST = "SRN-Status-Update-To-Test";
        private const string MEMBER_AUTO_CLOSE_ON_SRN_CLOSURE = "Member-Auto-Close-on-SRN-Closure";
        private const string REPLACEMENT_FILE_SUBMISSION_PROCESS_KEY = "Replacement-File-Submissions";
        private const string ADHOC_FILE_SUBMISSION_PROCESS_KEY = "Ad-Hoc-File-Submissions";

        private readonly GlobalHelper _globalHelper;
        private readonly NewSrnApplicationUserService _newSrnApplicationUserService;

        public TasksController(CamundaService tasksService, GlobalHelper globalHelper, NewSrnApplicationUserService newSrnApplicationUserService)
        {
            _camundaService = tasksService;
            _globalHelper = globalHelper;
            _newSrnApplicationUserService = newSrnApplicationUserService;
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet]
        [ProducesResponseType(typeof(List<ShmMemberTaskOutputDTO>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetStakeHolderManagerMemberTasks()
        {
            var definitionKeys = new List<string>
                {
                    MEMBER_TAKEON_PROCESS_KEY,
                    MEMBER_UPDATE_PROCESS_KEY,
                    MEMBER_AUTO_CLOSE_ON_SRN_CLOSURE
                };
            
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            return Ok(await _camundaService.GetStakeholderManagerMemberTasks(user, definitionKeys));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet]
        [ProducesResponseType(typeof(ShmSRNTaskOutputDTO), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetStakeHolderManagerSRNTasks()
        {
            var definitionKeys = new List<string>
                {
                    SRN_APPLICATION_PROCESS_KEY,
                    SRN_UPDATE_PROCESS_KEY,
                    SRN_MERGE_SPLIT_SALE_PROCESS_KEY,
                    SRN_STATUS_UPDATE,
                    SRN_STATUS_UPDATE_TO_TEST
                };
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            return Ok(await _camundaService.GetStakeHolderManagerSRNTasks(user, definitionKeys));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet]
        [ProducesResponseType(typeof(List<ShmReplacementFileTaskOutputDTO>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<List<ShmReplacementFileTaskOutputDTO>> GetStakeHolderManagerReplacementFileTasks()
        {
            var definitionKeys = new List<string>
                {
                    REPLACEMENT_FILE_SUBMISSION_PROCESS_KEY
                }; 
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);


            return await _camundaService.GetStakeHolderManagerReplacementFileTasks(definitionKeys, user);
        }

        [Authorize(Roles = "Financial Administrator,Group Stakeholder Manager")]
        [HttpGet]
        [ProducesResponseType(typeof(FinancialAdminMemberTasksOutputDTO), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetFinancialAdminMemberTasks()
        {
            List<string> definitionKeys = new List<string>
                {
                    MEMBER_TAKEON_PROCESS_KEY,
                    MEMBER_STATUS_UPDATE
                };
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            return Ok(await _camundaService.GetFinancialAdminMemberTasks(user, definitionKeys));
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpGet]
        [ProducesResponseType(typeof(GSHMShmTasksOutputDTO), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetGSHMShmTasks()
        {
            return Ok(await _camundaService.GetGSHMShmTasks());
        }

        [Authorize(Roles = "SACRRA Administrator")]
        [HttpGet]
        [ProducesResponseType(typeof(List<SacrraAdminMyTasksOutputDTO>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetSACRRAAdminTasks()
        {
            List<string> definitionKeys = new List<string>
                {
                    SRN_APPLICATION_PROCESS_KEY,
                    SRN_UPDATE_PROCESS_KEY,
                    SRN_MERGE_SPLIT_SALE_PROCESS_KEY,
                    SRN_STATUS_UPDATE,
                    MEMBER_STATUS_UPDATE,
                    SRN_STATUS_UPDATE_NON_CANCELLATIONS,
                    SRN_STATUS_UPDATE_TO_TEST
                };


            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            return Ok(await _camundaService.GetSACRRAAdminTasks(user, definitionKeys));
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpGet]
        [ProducesResponseType(typeof(GSHMUnassignedTasksOutputDTO), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetGSHMUnassignedTasks()
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            return Ok(await _camundaService.GetGSHMUnassignedTasks(user));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteSHMReviewMemberApplicationTask(string taskId, [FromBody] SHMMemberReviewInputDTO reviewDTO)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            await _camundaService.CompleteSHMReviewMemberApplicationTask(taskId, reviewDTO, user);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompletePmSrnReviewTask(string taskId, [FromBody] PmSrnReviewInputDto pmSrnReviewInputDto)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            if (user == null)
            {
                throw new Exception("User not found");
            }
            
            _newSrnApplicationUserService.CompletePmSrnReviewTask(taskId, pmSrnReviewInputDto, user);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpPut("{taskId}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteFinancialAdminCheckPaymentTask(string taskId, [FromBody] FinancialAdminMemberReviewInputDTO reviewDTO)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            await _camundaService.CompleteFinancialAdminCheckPaymentTask(taskId, reviewDTO, user);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpPut("{taskId}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteFinancialAdminCheckAssessmentPaymentTask(string taskId, [FromBody] FinancialAdminMemberReviewInputDTO reviewDTO)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            await _camundaService.CompleteFinancialAdminCheckAssessmentInvoicePaymentTask(taskId, reviewDTO, user);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteSHMSRNSecondReviewTask(string taskId, [FromBody] SHMSRNReviewInputDTO reviewDTO)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            await _camundaService.CompleteSHMSRNSecondReviewTask(taskId, reviewDTO, user);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpPut("{taskId}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteFinancialAdminCheckOnboardingPaymentTask(string taskId, [FromBody] FinancialAdminMemberReviewInputDTO reviewDTO)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            await _camundaService.CompleteFinancialAdminCheckOnboardingInvoicePaymentTask(taskId, reviewDTO, user);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpPut("{taskId}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteFinancialAdminCheckFullMemberPayment(string taskId, [FromBody] FinancialAdminMemberReviewInputDTO reviewDTO)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            await _camundaService.CompleteFinancialAdminCheckFullMemberPayment(taskId, reviewDTO, user);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("{taskId}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteStakeHolderManagerReviewMemberChangesTask(string taskId, [FromBody] StakeholderManagerMemberUpdateReviewInputDTO reviewDTO)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            await _camundaService.CompleteStakeHolderManagerReviewMemberChangesTask(taskId, reviewDTO, user);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("{taskId}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteStakeHolderManagerReviewSRNChangesTask(string taskId, [FromBody] TaskSRNChangesReviewDTO taskChangesReviewDTO)
        {
            await _camundaService.CompleteReviewSRNChangesTask(taskId, taskChangesReviewDTO);
            return Ok();
        }

        [Authorize(Roles = "SACRRA Administrator")]
        [HttpPut("{taskId}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteSACRRAAdminUpdateMemberUsersOnDTHTask(string taskId)
        {
            await _camundaService.CompleteSACRRAAdminUpdateMemberUsersOnDTHTask(taskId);
            return Ok();
        }

        [Authorize(Roles = "SACRRA Administrator")]
        [HttpPost]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<List<string>> CompleteTasksInBulk(string taskDefinitionKey, [FromBody] Dictionary<string, Dictionary<string, Dictionary<string, string>>> taskVariables)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            return await _camundaService.CompleteTasksInBulk(taskDefinitionKey, taskVariables, user);
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpPut("{taskId}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteFinancialdminUpdateMemberBillingTask(string taskId)
        {
            await _camundaService.CompleteFinancialdminUpdateMemberBillingTask(taskId);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet]
        [ProducesResponseType(typeof(List<DWExceptionTaskItemDTO>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetStakeHolderManagerDWExceptionTasks()
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            var tasks = await _camundaService.GetDWExceptionTasks(user);
            return Ok(tasks);
        }
        
        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("{taskId}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteStakeHolderManagerDWException(string taskId, [FromBody] TaskCompleteDWExceptionDTO taskReview)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            await _camundaService.CompleteStakeHolderManagerDWException(taskId, taskReview, user);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("{taskId}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteMemberAutoCloseSHMTask(string taskId, [FromBody] SHMCompleteMemberAutoCloseTaskDTO taskDecision)
        {
            await _camundaService.CompleteMemberAutoCloseSHMTask(taskId, taskDecision);
            return Ok();
        }

        [Authorize(Roles = "SACRRA Administrator,System Administrator")]
        [HttpPut("{emailAddress}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> BulkCompleteUserTasks(string emailAddress, string taskName = null,  string processDefinitionKey = null)
        {
            await _camundaService.BulkCompleteUserTasks(emailAddress, taskName, processDefinitionKey);
            return Ok();
        }

        [Authorize(Roles = "SACRRA Administrator,System Administrator")]
        [HttpPost]
        [ProducesResponseType(typeof(MigrationPlanGenerationOutputDTO), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GenerateProcessDefinitionMigrationPlan([FromBody] MigrationPlanGenerationDTO migrationPlanGenerationDTO)
        {
            var migrationPlan = await _camundaService.GenerateProcessDefinitionMigrationPlan(migrationPlanGenerationDTO);
            return Ok(migrationPlan);
        }

        [Authorize(Roles = "SACRRA Administrator,System Administrator")]
        [HttpPost]
        [ProducesResponseType(typeof(MigrationPlanValidationOutputDTO), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> ValidateProcessDefinitionMigrationPlan([FromBody] MigrationPlanValidationInputDTO migrationPlanValidationDTO)
        {
            var migrationPlan = await _camundaService.ValidateProcessDefinitionMigrationPlan(migrationPlanValidationDTO);
            return Ok(migrationPlan);
        }

        [Authorize(Roles = "SACRRA Administrator,System Administrator")]
        [HttpPost]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        [ProducesResponseType(typeof(void), 500)]
        public async Task<IActionResult> ExecuteProcessDefinitionMigrationPlan([FromBody] MigrationPlanExecutionInputDTO migrationPlanExecutionDTO)
        {
            try
            {
                await _camundaService.ExecuteProcessDefinitionMigrationPlan(migrationPlanExecutionDTO);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [Authorize(Roles = "SACRRA Administrator,System Administrator")]
        [HttpPut]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        [ProducesResponseType(typeof(void), 500)]
        public async Task<IActionResult> AddTestEndDateVariableToSRNProcessInstances()
        {
            try
            {
                await _camundaService.AddTestEndDateVariableToSRNProcessInstances();
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [Authorize(Roles = "SACRRA Administrator,System Administrator")]
        [HttpGet]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        [ProducesResponseType(typeof(void), 500)]
        public async Task<List<SRNHasTaskDTO>> GetSRNTasksBySRNNumber(string srnNumber)
        {

            var sRNHasTaskDTO = await _camundaService.GetSRNTasksBySRNNumber(srnNumber);
           
            return sRNHasTaskDTO;
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("{taskId}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteStakeholderManagerReplacementFileSubmissionRequest(string taskId, [FromBody] StakeholderManagerReplacementFileRequestReviewInputDTO inputDTO)
        {
            _camundaService.CompleteStakeholderManagerReplacementFileSubmissionRequest(taskId, inputDTO);
            return Ok();
        }

        [Authorize(Roles = "SACRRA Administrator, System Administrator, Stakeholder Manager")]
        [HttpPost]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        [ProducesResponseType(typeof(void), 500)]
        public RestResponse CancelReplacementFile([FromBody] CompleteReplacementFileDueInputDTO cancelReplacementFileRequestInputDTO)
        {
            return _camundaService.CancelReplacementFile(cancelReplacementFileRequestInputDTO);
        }

        [Authorize(Roles = "SACRRA Administrator, System Administrator, Stakeholder Manager")]
        [HttpPost]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        [ProducesResponseType(typeof(void), 500)]
        public RestResponse PostponeReplacementFile([FromBody] CompleteReplacementFileDueInputDTO cancelReplacementFileRequestInputDTO)
        {
            return _camundaService.PostponeReplacementFile(cancelReplacementFileRequestInputDTO);
        }

        [Authorize(Roles = "SACRRA Administrator, System Administrator, Stakeholder Manager")]
        [HttpPost]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        [ProducesResponseType(typeof(void), 500)]
        public RestResponse RecheckDthSubmission([FromBody] CompleteReplacementFileDueInputDTO cancelReplacementFileRequestInputDTO)
        {
            return _camundaService.RecheckDthSubmission(cancelReplacementFileRequestInputDTO);
        }

        [Authorize(Roles = "SACRRA Administrator, System Administrator, Stakeholder Manager")]
        [HttpPost]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        [ProducesResponseType(typeof(void), 500)]
        public RestResponse SHMReviewReplacementFileSubmitted([FromBody] CompleteReplacementFileDueInputDTO shmReviewReplacementFileSubmittedInputDTO)
        {
            return _camundaService.SHMReviewReplacementFileSubmitted(shmReviewReplacementFileSubmittedInputDTO);
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("{taskId}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteStakeholderManagerAdhocFileSubmissionRequest(string taskId, [FromBody] StakeholderManagerAdhocFileRequestReviewInputDTO inputDTO)
        {
            _camundaService.CompleteStakeholderManagerAdHocFileSubmissionRequest(taskId, inputDTO);
            return Ok();
        }

        [Authorize(Roles = "SACRRA Administrator, System Administrator, Stakeholder Manager")]
        [HttpPost]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        [ProducesResponseType(typeof(void), 500)]
        public RestResponse CancelAdhocFile([FromBody] CompleteAdhocFileDueInputDTO cancelAdhocFileRequestInputDTO)
        {
            return _camundaService.CancelAdhocFile(cancelAdhocFileRequestInputDTO);
        }

        [Authorize(Roles = "SACRRA Administrator, System Administrator, Stakeholder Manager")]
        [HttpPost]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        [ProducesResponseType(typeof(void), 500)]
        public RestResponse PostponeAdhocFile([FromBody] CompleteAdhocFileDueInputDTO cancelAdhocFileRequestInputDTO)
        {
            return _camundaService.PostponeAdhocFile(cancelAdhocFileRequestInputDTO);
        }

        [Authorize(Roles = "SACRRA Administrator, System Administrator, Stakeholder Manager")]
        [HttpPost]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        [ProducesResponseType(typeof(void), 500)]
        public RestResponse RecheckAdhocDthSubmission([FromBody] CompleteAdhocFileDueInputDTO cancelAdhocFileRequestInputDTO)
        {
            return _camundaService.RecheckAdhocDthSubmission(cancelAdhocFileRequestInputDTO);
        }

        [Authorize(Roles = "SACRRA Administrator, System Administrator, Stakeholder Manager")]
        [HttpPost]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        [ProducesResponseType(typeof(void), 500)]
        public RestResponse SHMReviewAdhocFileSubmitted([FromBody] CompleteAdhocFileDueInputDTO shmReviewAdhocFileSubmittedInputDTO)
        {
            return _camundaService.SHMReviewAdhocFileSubmitted(shmReviewAdhocFileSubmittedInputDTO);
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet]
        [ProducesResponseType(typeof(List<ShmReplacementFileTaskOutputDTO>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<List<ShmAdhocFileTaskOutputDTO>> GetStakeHolderAdhocFileTasks()
        {
            var definitionKeys = new List<string>
                {
                    ADHOC_FILE_SUBMISSION_PROCESS_KEY
                };
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            return await _camundaService.GetStakeHolderManagerAdhocFileTasks(definitionKeys, user);
        }
    }
}
