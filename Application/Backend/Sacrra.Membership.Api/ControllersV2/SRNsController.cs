using System;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.DTOs.SRNReTestingDTOs;
using Sacrra.Membership.Business.DTOs.SRNSummaryDTOs;
using Sacrra.Membership.Business.DTOs.SRNUpdateDTOs;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using System.Collections.Generic;

namespace Sacrra.Membership.Api.ControllersV2
{
    [ApiController]
    [ApiVersion("2")]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    [Authorize(Roles = "Financial Administrator,Member,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,Bureau,ALG Leader")]
    public class SRNsController : Controller
    {
        private readonly GlobalHelper _globalHelper;
        private readonly SRNService _srnService;

        public SRNsController(GlobalHelper globalHelper, SRNService srnService)
        {
            _globalHelper = globalHelper;
            _srnService = srnService;
        }

        [HttpGet("{srnID}")]
        [ProducesResponseType(type: typeof(List<SRNGetOutputDTO>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetSRN(int srnID, SRNStatusFileTypes? fileType, string? taskId = null)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            return Ok(_srnService.GetSRN(srnID, user, taskId, fileType));
        }

        [Authorize(Roles = "Stakeholder Manager")]
        [HttpGet("{srnID}")]
        [ProducesResponseType(type: typeof(List<SRNGetOutputDTO>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetSRNForSRNUpdateTask(int srnID, SRNStatusFileTypes? fileType)
        {
            return Ok(_srnService.GetSRNForSRNUpdateTask(srnID, fileType));
        }

        [HttpPut]
        [ProducesResponseType(typeof(SRNGetOutputDTO), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult UpdateSRN([FromBody] SRNUpdateInputDTO srnUpdateInputDTO)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            return Ok(_srnService.UpdateSRN(srnUpdateInputDTO, user));
        }

        [HttpGet("tradingnames/is-possible-duplicate/{srnId}/{tradingName}")]
        [ProducesResponseType(type: typeof(bool), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult IsPossibleDuplicateTradingName(int srnId, string tradingName)
        {
            return Ok(_globalHelper.IsPossibleDuplicateTradingName(srnId, tradingName));
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(List<SRNRolloutScheduleOutputDTO>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetRolloutSchedule()
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            return Ok(_srnService.GetRolloutSchedule(user));
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(List<SRNSummaryDetailsOutputDTO>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetSRNSummaryDetails()
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            return Ok(_srnService.GetSRNSummaryDetails(user));
        }

        [ProducesResponseType(type: typeof(IEnumerable<SRNSummaryAllDetailsOutputDTO>), statusCode: 200)]
        [ProducesResponseType(type: typeof(IEnumerable<SRNSummaryAllDetailsBureauOutputDTO>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetSRNSummaryAllDetails()
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            if (user.RoleId == UserRoles.Bureau)
            {
                return Ok(_srnService.GetSRNSummaryAllDetailsBureau(user));
            }

            return Ok(_srnService.GetSRNSummaryAllDetails(user));
        }

        [HttpPost]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public void CreateSrnEntries([FromBody] List<SRNRequestInputDTO> srnEntryList)
        {
            _srnService.CreateSrnEntries(srnEntryList, _globalHelper.GetUserByAuth0Id(User.Identity.Name));
        }

        [HttpGet("{memberId}")]
        [ProducesResponseType(type: typeof(List<BranchLocationOutputDTO>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetBranchLocations(int memberId)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            var entity = _srnService.GetBranchLocations(memberId, user);
            if (entity == null)
                return NotFound();

            return Ok(entity);
        }

        [HttpGet("{memberId}")]
        [ProducesResponseType(type: typeof(List<SRNViewOutputDTO>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult ListSRNsByMemberId(int memberId, bool withSrnNumberOnly = false, bool? isActivityAllowedWhileInProcess = null)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            var entity = _srnService.ListSRNsByMemberId(memberId, user, withSrnNumberOnly, isActivityAllowedWhileInProcess);
            if (entity == null)
                return NotFound();

            return Ok(entity);
        }

        [Authorize(Roles = "Stakeholder Manager, Group Stakeholder Manager")]
        [HttpPost]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult RequestSRNSStatusUpdate([FromBody] List<SRNStatusUpdateRequestDTO> requests)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);


            _srnService.RequestSRNStatusUpdate(requests, user);
            return Ok();
        }

        [HttpGet("{memberId}")]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetSPNumbers(int memberId)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            return Ok( _srnService.GetSPNumbers(memberId, user));
        }

        [HttpGet("{memberId}")]
        [ProducesResponseType(type: typeof(List<SRNReTestingOutputDTO>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetLiveSRNFileList(int memberId)
        {
            return Ok(_srnService.GetLiveSRNFileList(memberId));
        }

        [HttpPost]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult UpdateSRNTestingStatus([FromBody] SRNRequestReTestingInputDTO SRNData)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            _srnService.updateSRNTestingStatus(SRNData, user);

            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager")]
        [HttpPut]
        [ProducesResponseType(typeof(SRNGetOutputDTO), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult UpdateSRNWhileInProcess([FromBody] SRNUpdateInputDTO srnUpdateInputDTO)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            _srnService.UpdateSRNWhileInProcess(srnUpdateInputDTO, user);

            return Ok();
        }
        
        [HttpGet]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetSrnSummaryFileExtract()
        {
            if (User.Identity == null) throw new Exception("User Identity is null");
            
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);
            
            return Ok(_srnService.GetSrnSummaryFileExtract(user));
        }
    }
}
