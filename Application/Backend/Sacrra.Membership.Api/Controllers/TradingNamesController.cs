using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Api.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.IdValuePair;
using System.Collections.Generic;

using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api.Controllers
{
    [Authorize(Roles = "Member,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,ALG Leader")]
    [Produces("application/json")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class TradingNamesController : Controller
    {
        private readonly TradingNameRepository _repository;

        public TradingNamesController(TradingNameRepository TradingNameRepository)
        {
            _repository = TradingNameRepository;
        }

        [HttpGet("{id}", Name = "GetTradingName")]
        [ProducesResponseType(typeof(TradingNameGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult Get(int id)
        {
            return Ok(_repository.Get(id));
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult List([FromQuery]NameListParams listParams)
        {
            var TradingNames = _repository.List(listParams, User);

            Response.AddPagination(TradingNames.CurrentPage, TradingNames.PageSize, TradingNames.TotalCount, TradingNames.TotalPages);

            return Ok(TradingNames);
        }

        [HttpPost]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult Create([FromBody]TradingNameCreateResource modelForCreate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var id = _repository.Create(modelForCreate);

            return CreatedAtRoute("GetTradingName", new { controller = "TradingNames", id }, id);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(typeof(TradingNameGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult Update(int id, [FromBody]TradingNameUpdateResource modelForUpdate)
        {
            // validate request
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            return Ok(_repository.Update(modelForUpdate));
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult Delete(int id)
        {
            _repository.Delete(id);
            return Ok();
        }
    }
}
