using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Api.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Business.Resources.SPGroup;
using Sacrra.Membership.Business.Resources.SRN;
using Sacrra.Membership.Business.Resources.SRNChangeRequest;
using Sacrra.Membership.Business.Resources.SRNContact;
using Sacrra.Membership.Database.Enums;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api.Controllers
{
    [Authorize(Roles = "Financial Administrator,Member,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,Bureau,ALG Leader")]
    [Produces("application/json")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class SRNsController : Controller
    {
        private readonly SRNRepository _repository;

        public SRNsController(SRNRepository SRNRepository)
        {
            _repository = SRNRepository;
        }

        [HttpGet("{id}", Name = "GetSRN")]
        [ProducesResponseType(typeof(SRNGetResource), 200)]
        [ProducesResponseType(typeof(SRNGetResource), 404)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> Get(int id, SRNStatusFileTypes? fileType)
        {
            var entity = await _repository.Get(id, fileType);

            if (entity == null)
                return NotFound();

            return Ok(entity);
        }

        [HttpGet("{id}/v2", Name = "GetSRNV2")]
        [ProducesResponseType(typeof(SRNGetV2Resource), 200)]
        [ProducesResponseType(typeof(SRNGetV2Resource), 404)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> GetV2(int id, SRNStatusFileTypes? fileType)
        {
            var entity = await _repository.GetV2(id, fileType);

            if (entity == null)
                return NotFound();

            return Ok(entity);
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(List<SRNGetResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> List([FromQuery]NameListParams listParams)
        {
            var SRNs = await _repository.List(listParams);

            Response.AddPagination(SRNs.CurrentPage, SRNs.PageSize, SRNs.TotalCount, SRNs.TotalPages);

            return Ok(SRNs);
        }

        [HttpGet("srn-summary")]
        [ProducesResponseType(type: typeof(List<SRNSummaryResourceSimple>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> ListSRNSummary([FromQuery]NameListParams listParams)
        {
            var SRNs = await _repository.ListSRNSummary();

            return Ok(SRNs);
        }
        [HttpGet("srn-rollout-statuses")]
        [ProducesResponseType(type: typeof(List<SRNRolloutStatusResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> ListSRNRolloutStatuses([FromQuery] NameListParams listParams)
        {
            var data = await _repository.ListSRNRolloutStatuses();

            return Ok(data);
        }

        [HttpGet("export-rollout-statuses")]
        [ProducesResponseType(type: typeof(List<SRNRolloutStatusResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> ExportSRNRolloutStatuses([FromQuery] NameListParams listParams)
        {
            var data = await _repository.ExportSRNRolloutStatuses();

            return Ok(data);
        }

        [HttpGet("srn-summary/export")]
        [ProducesResponseType(type: typeof(List<SRNSummaryExportResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> ExportSRNSummary([FromQuery] NameListParams listParams)
        {
            var SRNs = await _repository.ExportSRNSummary();

            return Ok(SRNs);
        }

        [HttpPost("{userId}")]
        [ProducesResponseType(typeof(List<int>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> Create([FromBody]List<SRNCreateResource> modelForCreate, int userId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var auth0Id = HttpContext.User.Identity.Name;

            var ids = await _repository.Create(modelForCreate, auth0Id);
            return Created("GetSRN", ids);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(typeof(SRNGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> Update(int id, [FromBody]SRNUpdateResource modelForUpdate)
        {
            // validate request
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            return Ok(await _repository.Update(modelForUpdate));
        }

        [Authorize(Roles = "System Administrator")]
        [HttpDelete("{id}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> Delete(int id)
        {
            await _repository.Delete(id);
            return Ok();
        }

        [HttpGet("{id}/contacts")]
        [ProducesResponseType(type: typeof(List<SRNContactGetResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> Contacts(int id)
        {
            return Ok(await _repository.Contacts(id));
        }

        [HttpPut("contacts/{id}")]
        [ProducesResponseType(typeof(SRNContactGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> UpdateContact(int id, [FromBody]SRNContactUpdateResource modelForUpdate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            return Ok(await _repository.UpdateContact(id, modelForUpdate));
        }

        [HttpGet("{memberId}/tradingnames")]
        [ProducesResponseType(type: typeof(List<string>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> GetTradingNames(int memberId)
        {
            return Ok(await _repository.GetTradingNames(memberId));
        }

        [HttpPost("{memberId}/generate-sp-number")]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        [ProducesResponseType(typeof(List<IdValuePairResource>), 201)]

        public async Task<IActionResult> CreateSPNumber(int memberId)
        {
            if (memberId <= 0)
            {
                return BadRequest(ModelState);
            }

            var spNumbers = await _repository.CreateSPNumber(memberId, User);

            return CreatedAtRoute("GetSPNumbers", new { controller = "SRNs", memberId }, spNumbers);
        }

        [HttpGet("{memberId}/spgroups", Name = "GetSPGroups")]
        [ProducesResponseType(type: typeof(List<SPGroupGetResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> GetSPGroups(int memberId)
        {
            return Ok(await _repository.GetSPGroups(memberId));
        }

        [HttpGet("{memberId}/spnumbers", Name = "GetSPNumbers")]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> GetSPNumbers(int memberId)
        {
            return Ok(await _repository.GetSPNumbers(memberId));
        }

        [HttpGet("{memberId}/branchlocations")]
        [ProducesResponseType(typeof(List<IdValuePairResource>), 200)]
        [ProducesResponseType(typeof(List<IdValuePairResource>), 404)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> GetBranchLocations(int memberId)
        {
            var entity = await _repository.GetBranchLocations(memberId);
            if (entity == null)
                return NotFound();

            return Ok(entity);
        }

        [HttpGet("list/{memberId}")]
        [ProducesResponseType(type: typeof(List<SRNGetResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> ListByMemberId(int memberId, bool withSrnNumberOnly = false, bool? isActivityAllowedWhileInProcess = null)
        {
            return Ok(await _repository.ListByMemberId(memberId, withSrnNumberOnly, isActivityAllowedWhileInProcess));
        }

        [HttpGet("list/{memberId}/status")]
        [ProducesResponseType(type: typeof(List<SRNGetResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> ListByMemberId(int memberId, string status)
        {
            return Ok(await _repository.ListByMemberId(memberId, status));
        }

        [HttpGet("{id}/change-request")]
        [ProducesResponseType(typeof(SRNChangeRequestGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetSRNStagingChangeRequest(int id)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            return Ok(await _repository.GetStagingChangeRequest(id));
        }

        [HttpPost("request-merge")]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        [ProducesResponseType(typeof(List<IdValuePairResource>), 201)]

        public async Task<IActionResult> RequestSRNMerge([FromBody]SRNMergeRequestResource request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            await _repository.RequestSRNMerge(request);
            return Ok();
        }

        [HttpPost("request-split")]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        [ProducesResponseType(typeof(List<IdValuePairResource>), 201)]

        public async Task<IActionResult> RequestSRNSplit([FromBody]SRNSplitRequestResource request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            await _repository.RequestSRNSplit(request);
            return Ok();
        }

        [HttpPost("request-sale")]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        [ProducesResponseType(typeof(List<IdValuePairResource>), 201)]

        public async Task<IActionResult> RequestSRNSale([FromBody]SRNSaleRequestResource request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            await _repository.RequestSRNSale(request);
            return Ok();
        }

        [HttpGet("tradingnames/check-if-exists/{tradingName}")]
        [ProducesResponseType(type: typeof(bool), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> DoesTradingNameExist(string tradingName)
        {
            return Ok(await _repository.DoesTradingNameExist(tradingName));
        }

        [HttpGet("tradingnames/check-if-similar-exists/{tradingName}")]
        [ProducesResponseType(type: typeof(bool), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult DoesSimilarTradingNameExist(string tradingName)
        {
            return Ok(_repository.DoesSimilarTradingNameExist(tradingName));
        }
        [HttpGet("tradingnames/is-possible-duplicate/{srnId}/{tradingName}")]
        [ProducesResponseType(type: typeof(bool), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult IsPossibleDuplicateTradingName(int srnId, string tradingName)
        {
            return Ok(_repository.IsPossibleDuplicateTradingName(srnId, tradingName));
        }

        [Authorize(Roles = "Stakeholder Manager, Group Stakeholder Manager")]
        [HttpPost("request-status-update")]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> RequestSRNSStatusUpdate([FromBody] List<SRNStatusUpdateRequestResource> requests)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            await _repository.RequestSRNStatusUpdate(requests);
            return Ok();
        }
    }
}
