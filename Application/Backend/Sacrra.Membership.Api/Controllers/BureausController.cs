using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Api.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources.IdValuePair;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api.Controllers
{
    [Produces("application/json")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class BureausController : Controller
    {
        private readonly BureauRepository _repository;

        public BureausController(BureauRepository BureauRepository)
        {
            _repository = BureauRepository;
        }
        [Authorize]
        [HttpGet]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> List([FromQuery]NameListParams listParams)
        {
            var Bureaus = await _repository.GetAll(listParams);

            Response.AddPagination(Bureaus.CurrentPage, Bureaus.PageSize, Bureaus.TotalCount, Bureaus.TotalPages);

            return Ok(Bureaus);
        }
    }
}
