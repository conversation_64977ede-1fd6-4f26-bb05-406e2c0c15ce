using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Business.Resources;
using System.Collections.Generic;

using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api.Controllers
{
    [Produces("application/json")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class SRNStatusReasonsController : Controller
    {
        private readonly SRNStatusReasonRepository _repository;

        public SRNStatusReasonsController(SRNStatusReasonRepository repository)
        {
            _repository = repository;
        }

        [Authorize(Roles = "Group Stakeholder Manager, Stakeholder Manager, Financial Administrator")]
        [HttpGet]
        [ProducesResponseType(type: typeof(List<SRNStatusReasonCustomGetResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult Get([FromQuery] List<string> statusesToBeExcluded = null)
        {
            var data = _repository.Get(statusesToBeExcluded);

            return Ok(data);
        }
    }
}
