using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api.Controllers
{
    [Produces("application/json")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class TasksController : Controller
    {
        private readonly CamundaRepository _repository;
        private const string MEMBER_TAKEON_PROCESS_KEY = "New-Member-Takeon";
        private const string SRN_APPLICATION_PROCESS_KEY = "New-SRN-Application";
        private const string MEMBER_UPDATE_PROCESS_KEY = "Member-Update-Details";
        private const string SRN_UPDATE_PROCESS_KEY = "SRN-Update-Details";
        private const string SRN_MERGE_SPLIT_SALE_PROCESS_KEY = "SRN-Split-Merge-Sell";
        private const string MEMBER_STATUS_UPDATE = "Member-Status-Update";
        private const string SRN_STATUS_UPDATE = "SRN-Status-Update";
        private const string SRN_STATUS_UPDATE_NON_CANCELLATIONS = "SRN-Status-Update-Non-Cancellations";
        private const string SRN_STATUS_UPDATE_TO_TEST = "SRN-Status-Update-To-Test";

        public TasksController(CamundaRepository repository)
        {
            _repository = repository;
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,Financial Administrator")]
        [HttpGet("member")]
        [ProducesResponseType(typeof(List<UserTaskListGetResource>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetMemberTasks()
        {
            List<string> definitionKeys = new List<string>
                {
                    MEMBER_TAKEON_PROCESS_KEY,
                    MEMBER_UPDATE_PROCESS_KEY
                };
            return Ok(await _repository.Tasks(definitionKeys));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,Financial Administrator")]
        [HttpGet("srn")]
        [ProducesResponseType(typeof(List<UserTaskListGetResource>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetSRNTasks()
        {
            List<string> definitionKeys = new List<string>
                {
                    SRN_APPLICATION_PROCESS_KEY,
                    SRN_UPDATE_PROCESS_KEY,
                    SRN_MERGE_SPLIT_SALE_PROCESS_KEY
                };
            return Ok(await _repository.Tasks(definitionKeys));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholderadmin/{id}/member")]
        [ProducesResponseType(typeof(List<UserTaskListGetResource>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> GetStakeHolderAdminMemberTasks(int id)
        {
            return Ok(await _repository.GetStakeHolderAdminTasks(id, MEMBER_TAKEON_PROCESS_KEY));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholderadmin/{id}/srn")]
        [ProducesResponseType(typeof(List<UserTaskListGetResource>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> GetStakeHolderAdminSRNTasks(int id)
        {
            return Ok(await _repository.GetStakeHolderAdminTasks(id, SRN_APPLICATION_PROCESS_KEY));
        }

        [Authorize(Roles = "Financial Administrator,Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholderadmin/{id}/all")]
        [ProducesResponseType(typeof(List<UserTaskListGetResource>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> GetAllStakeHolderAdminTasks(int id)
        {
            return Ok(await _repository.GetStakeHolderAdminTasks(id));
        }

        [Authorize(Roles = "SACRRA Administrator")]
        [HttpGet("list/sacrraadmin/{id}/srn")]
        [ProducesResponseType(typeof(List<StakeHolderManagerTaskListResource>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> GetSACRRAAdminSRNTasks(string id)
        {
            List<string> definitionKeys = new List<string>
                {
                    SRN_APPLICATION_PROCESS_KEY,
                    SRN_UPDATE_PROCESS_KEY,
                    SRN_MERGE_SPLIT_SALE_PROCESS_KEY,
                    SRN_STATUS_UPDATE,
                    MEMBER_STATUS_UPDATE,
                    SRN_STATUS_UPDATE_NON_CANCELLATIONS,
                    SRN_STATUS_UPDATE_TO_TEST
                };

            return Ok(await _repository.GetStakeHolderManagerTasks(id, definitionKeys));
        }

        [Authorize(Roles = "Financial Administrator,Group Stakeholder Manager")]
        [HttpGet("list/financialadmin/{id}/member")]
        [ProducesResponseType(typeof(GenericTaskListResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetFinancialAdminMemberTasks(string id)
        {
            List<string> definitionKeys = new List<string>
                {
                    MEMBER_TAKEON_PROCESS_KEY,
                    MEMBER_STATUS_UPDATE
                };

            return Ok(await _repository.GetFinancialAdminTasks(id, definitionKeys));
        }

        [Authorize(Roles = "Financial Administrator,Group Stakeholder Manager")]
        [HttpGet("list/financialadmin/{id}/srn")]
        [ProducesResponseType(typeof(GenericTaskListResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetFinancialAdminSRNTasks(string id)
        {
            List<string> definitionKeys = new List<string>
                {
                    SRN_APPLICATION_PROCESS_KEY,
                    SRN_STATUS_UPDATE
                };

            return Ok(await _repository.GetFinancialAdminTasks(id, definitionKeys));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholdermanager/{id}/member")]
        [ProducesResponseType(typeof(StakeHolderManagerTaskListResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetStakeHolderManagerMemberTasks(string id)
        {
            List<string> definitionKeys = new List<string>
                {
                    MEMBER_TAKEON_PROCESS_KEY,
                    MEMBER_UPDATE_PROCESS_KEY
                };

            return Ok(await _repository.GetStakeHolderManagerTasks(id, definitionKeys));
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpGet("list/financialadministrator/{id}/statusupdate")]
        [ProducesResponseType(typeof(StakeHolderManagerTaskListResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetFinancialAdminTasks(string id)
        {
            List<string> definitionKeys = new List<string>
                {
                    MEMBER_STATUS_UPDATE
                };

            return Ok(await _repository.GetFinancialAdminTasks(id, definitionKeys));
        }

        [Authorize(Roles = "SACRRA Administrator")]
        [HttpGet("list/sacrraadministrator/{id}/statusupdate")]
        [ProducesResponseType(typeof(StakeHolderManagerTaskListResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetSacrraAdminStatusUpdateTasks(string id)
        {
            List<string> definitionKeys = new List<string>
                {
                    MEMBER_STATUS_UPDATE,
                    SRN_STATUS_UPDATE,
                    SRN_STATUS_UPDATE_NON_CANCELLATIONS,
                    SRN_STATUS_UPDATE_TO_TEST
                };

            return Ok(await _repository.GetSacrraAdminTasks(id, definitionKeys));
        }

        [Authorize(Roles = "Stakeholder Manager")]
        [HttpGet("list/stakeholdermanager/{id}/statusupdate")]
        [ProducesResponseType(typeof(StakeHolderManagerTaskListResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetSHMStatusUpdateTasks(string id)
        {
            List<string> definitionKeys = new List<string>
                {
                    SRN_STATUS_UPDATE,
                    SRN_STATUS_UPDATE_TO_TEST
                };

            return Ok(await _repository.GetStakeHolderManagerTasks(id, definitionKeys));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholdermanager/{id}/srn")]
        [ProducesResponseType(typeof(StakeHolderManagerTaskListResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetStakeHolderManagerSRNTasks(string id)
        {
            List<string> definitionKeys = new List<string>
                {
                    SRN_APPLICATION_PROCESS_KEY,
                    SRN_UPDATE_PROCESS_KEY,
                    SRN_MERGE_SPLIT_SALE_PROCESS_KEY,
                    SRN_STATUS_UPDATE,
                    SRN_STATUS_UPDATE_TO_TEST
                };
            return Ok(await _repository.GetStakeHolderManagerTasks(id, definitionKeys));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholdermanager/dw-exceptions")]
        [ProducesResponseType(typeof(DWExceptionTaskGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetStakeHolderManagerDWExceptionTasks()
        {
            var tasks = await _repository.GetDWExceptionTasks();
            return Ok(tasks);
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholdermanager/{taskId}/srn-merge")]
        [ProducesResponseType(typeof(TaskSRNMergeGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetSHMSRNMergeTask(string taskId)
        {
            return Ok(await _repository.GetSHMSRNMergeTask(taskId));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholdermanager/{taskId}/srn-split")]
        [ProducesResponseType(typeof(TaskSRNSplitGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetSHMSRNSplitTask(string taskId)
        {
            return Ok(await _repository.GetSHMSRNSplitTask(taskId));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholdermanager/{taskId}/srn-sale/seller")]
        [ProducesResponseType(typeof(TaskSRNSellerGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetSHMSellerSRNSaleTask(string taskId)
        {
            return Ok(await _repository.GetSHMSellerSRNSaleTask(taskId));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholdermanager/{taskId}/srn-sale/buyer")]
        [ProducesResponseType(typeof(TaskSRNBuyerGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> GetSHMBuyerSRNSaleTask(string taskId)
        {
            return Ok(await _repository.GetSHMBuyerSRNSaleTask(taskId));
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpPut("stakeholderadmin/{taskId}/member/allocate-shm")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteStakeHolderAdminTask(string taskId, [FromBody] TaskUpdateStakeHolderAdminResource task)
        {
            await _repository.CompleteStakeHolderAdminTask(taskId, task);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/member/review")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteStakeHolderManagerTask(string taskId, [FromBody] TaskUpdateStakeHolderManagerResource task)
        {
            await _repository.CompleteStakeHolderManagerReviewMemberApplicationTask(taskId, task);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/member/final-takeon")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteStakeHolderManagerFinalTakeOnTask(string taskId)
        {
            await _repository.CompleteStakeHolderManagerFinalTakeOnTask(taskId);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/member/review-changes")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteStakeHolderManagerReviewMemberChangesTask(string taskId, [FromBody] TaskMemberChangesReviewResource taskChangesReviewResource)
        {
            await _repository.CompleteStakeHolderManagerReviewMemberChangesTask(taskId, taskChangesReviewResource);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpPut("financialadmin/{taskId}/member/generate-full-member-invoice")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteFinancialAdminGenerateInvoiceTask(string taskId)
        {
            await _repository.CompleteFinancialAdminGenerateInvoiceTask(taskId);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpPut("financialadmin/{taskId}/member/check-full-member-payment")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteFinancialAdminCheckPaymentTask(string taskId, [FromBody] TaskUpdateFinancialAdminResource task)
        {
            await _repository.CompleteFinancialAdminCheckPaymentTask(taskId, task);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpPut("financialadmin/{taskId}/member/generate-assessment-invoice")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteFinancialAdminGenerateAssessmentInvoiceTask(string taskId)
        {
            await _repository.CompleteFinancialAdminGenerateAssessmentInvoiceTask(taskId);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpPut("financialadmin/{taskId}/member/check-assessment-invoice-payment")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteFinancialAdminCheckAssessmentPaymentTask(string taskId, [FromBody] TaskUpdateFinancialAdminResource taskUpdateResource)
        {
            await _repository.CompleteFinancialAdminCheckAssessmentInvoicePaymentTask(taskId, taskUpdateResource);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpPut("financialadmin/{taskId}/member/generate-onboarding-invoice")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteFinancialAdminGenerateOnboardingInvoiceTask(string taskId)
        {
            await _repository.CompleteFinancialAdminGenerateOnboardingInvoiceTask(taskId);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpPut("financialadmin/{taskId}/member/check-onboarding-invoice-payment")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteFinancialAdminCheckOnboardingPaymentTask(string taskId, [FromBody] TaskUpdateFinancialAdminResource taskUpdateResource)
        {
            await _repository.CompleteFinancialAdminCheckOnboardingInvoicePaymentTask(taskId, taskUpdateResource);
            return Ok();
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpPut("{taskId}/re-assign")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> ReAssignTask(string taskId, [FromBody] TaskUpdateResource task)
        {
            await _repository.ReAssignTask(taskId, task);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/first-review")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteStakeHolderManagerSRNFirstReviewTask(string taskId, [FromBody] TaskCompleteSRNReviewResource task)
        {
            await _repository.CompleteStakeHolderManagerSRNFirstReviewTask(taskId, task);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/assign/{userId}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteStakeHolderManagerSRNAssignSecondReviewerTask(string taskId, int userId)
        {
            await _repository.CompleteStakeHolderManagerSRNAssignToSecondReviewerTask(taskId, userId);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/second-review")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteStakeholderManagerSecondSRNReviewTask(string taskId, [FromBody] TaskCompleteSRNReviewResource task)
        {
            await _repository.CompleteStakeHolderManagerSecondSRNReviewTask(taskId, task);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/review-changes")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteStakeHolderManagerReviewSRNChangesTask(string taskId, [FromBody] TaskMemberChangesReviewResource taskChangesReviewResource)
        {
            await _repository.CompleteReviewSRNChangesTask(taskId, taskChangesReviewResource);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/review-sale/seller")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteSellerStakeHolderManagerSRNReviewSRNSale(string taskId, [FromBody] TaskSellerSHMReviewSRNSale taskReview)
        {
            await _repository.CompleteSellerStakeHolderManagerSRNReviewSRNSale(taskId, taskReview);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/review-sale/buyer")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteBuyerStakeHolderManagerSRNReviewSRNSale(string taskId, [FromBody] TaskBuyerSHMReviewSRNSale taskReview)
        {
            await _repository.CompleteBuyerStakeHolderManagerSRNReviewSRNSale(taskId, taskReview);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/review-merge")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteStakeholderManagerSrnMergeReview(string taskId, [FromBody] TaskSHMSrnMergeReviewPutResource taskReview)
        {
            await _repository.CompleteStakeholderManagerSrnMergeReview(taskId, taskReview);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/review-split")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteStakeholderManagerSrnSplitReview(string taskId, [FromBody] TaskSHMSrnSplitReviewPutResource taskReview)
        {
            await _repository.CompleteStakeholderManagerSrnSplitReview(taskId, taskReview);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/confirm-testing-migration")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteBuyerStakeHolderManagerConfirmSRNTestingAndMigration(string taskId, [FromBody] TaskBuyerSHMConfirmSRNTestingMigrationResource taskReview)
        {
            await _repository.CompleteBuyerStakeHolderManagerConfirmSRNTestingAndMigration(taskId, taskReview);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/dw-exceptions/complete-exception")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteStakeHolderManagerDWException(string taskId, [FromBody] TaskCompleteDWExceptionResource taskReview)
        {
            await _repository.CompleteStakeHolderManagerDWException(taskId, taskReview);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/create-unconfirmed-member")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteBuyerStakeHolderManagerCreateUnconfirmedBuyer(string taskId)
        {
            await _repository.CompleteBuyerStakeHolderManagerCreateUnconfirmedBuyer(taskId);
            return Ok();
        }

        [Authorize(Roles = "SACRRA Administrator, Stakeholder Manager, Financial Administrator")]
        [HttpPut("sacrraadmin/{taskId}/srn/takeon-updatedth")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteSACRRAAdminSRNTakeonUpdateDTHTask(string taskId)
        {
            await _repository.CompleteSACRRAAdminSRNTakeOnUpdateDTHTask(taskId);
            return Ok();
        }

        [Authorize(Roles = "SACRRA Administrator")]
        [HttpPut("sacrraadmin/{taskId}/srn/test-added-dth")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteSACRRAAdminSRNTestAddedToDTH(string taskId)
        {
            await _repository.CompleteSACRRAAdminSRNTestAddedToDTH(taskId);
            return Ok();
        }

        [Authorize(Roles = "SACRRA Administrator")]
        [HttpPut("sacrraadmin/{taskId}/srn/review-changes")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteSACRRAAdminReviewSRNChangesTask(string taskId, [FromBody] TaskMemberChangesReviewResource taskChangesReviewResource)
        {
            await _repository.CompleteReviewSRNChangesTask(taskId, taskChangesReviewResource);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/application/confirm-testing-migration")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteBuyerStakeHolderManagerSRNApplication_ConfirmSRNTesting(string taskId, [FromBody] TaskConfirmSRNTestingCompleteResource confirmation)
        {
            await _repository.CompleteBuyerStakeHolderManagerSRNApplication_ConfirmSRNTesting(taskId, confirmation);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/application/confirm-go-live")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteStakeHolderManagerSRNApplication_ConfirmSRNGoLive(string taskId, [FromBody] TaskConfirmSRNGoLiveResource confirmation)
        {
            await _repository.CompleteStakeHolderManagerSRNApplication_ConfirmSRNGoLive(taskId, confirmation);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/split-merge-sale/confirm-go-live")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteStakeHolderManagerSRNSaleSplitMerge_ConfirmSRNGoLive(string taskId, [FromBody] TaskConfirmSRNGoLiveResource confirmation)
        {
            await _repository.CompleteStakeHolderManagerSRNSaleSplitMerge_ConfirmSRNGoLive(taskId, confirmation);
            return Ok();
        }

        [Authorize(Roles = "SACRRA Administrator")]
        [HttpPut("sacrraadmin/{taskId}/srn/sale-split-merge/confirm-updated-dth")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteSACRRAAdminSRNSaleSplitMergeUpdateDTHTask(string taskId)
        {
            await _repository.CompleteSACRRAAdminSRNSaleSplitMergeUpdateDTHTask(taskId);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/status-update/confirm-file-submission")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteStakeholderManagerConfirmSRNFileSubmission(string taskId)
        {
            await _repository.CompleteStakeholderManagerConfirmSRNFileSubmission(taskId);
            return Ok();
        }

        [Authorize(Roles = "SACRRA Administrator")]
        [HttpPut("sacrraadmin/{taskId}/srn/status-update/confirm-updated-dth")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> CompleteSACRRAAdminSRNStatusChangedConfirmUpdateDTHTask(string taskId)
        {
            await _repository.CompleteSACRRAAdminSRNStatusChangedConfirmUpdateDTHTask(taskId);
            return Ok();
        }

        // [Authorize(Roles = "SACRRA Administrator")]
        [HttpPost("excute-timer-events")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> ExecuteTimerEvents([FromBody]List<string> processInstanceIds)
        {
            await _repository.ExecuteTimerEvents(processInstanceIds);
            return Ok();
        }
    }
}
