using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api.Controllers
{
    [Produces("application/json")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class TasksController : Controller
    {
        private readonly CamundaRepository _repository;
        private const string MEMBER_TAKEON_PROCESS_KEY = "New-Member-Takeon";
        private const string SRN_APPLICATION_PROCESS_KEY = "New-SRN-Application";
        private const string MEMBER_UPDATE_PROCESS_KEY = "Member-Update-Details";
        private const string SRN_UPDATE_PROCESS_KEY = "SRN-Update-Details";
        private const string SRN_MERGE_SPLIT_SALE_PROCESS_KEY = "SRN-Split-Merge-Sell";
        private const string MEMBER_STATUS_UPDATE = "Member-Status-Update";
        private const string SRN_STATUS_UPDATE = "SRN-Status-Update";
        private const string SRN_STATUS_UPDATE_NON_CANCELLATIONS = "SRN-Status-Update-Non-Cancellations";
        private const string SRN_STATUS_UPDATE_TO_TEST = "SRN-Status-Update-To-Test";

        public TasksController(CamundaRepository repository)
        {
            _repository = repository;
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,Financial Administrator")]
        [HttpGet("member")]
        [ProducesResponseType(typeof(List<UserTaskListGetResource>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetMemberTasks()
        {
            List<string> definitionKeys = new List<string>
                {
                    MEMBER_TAKEON_PROCESS_KEY,
                    MEMBER_UPDATE_PROCESS_KEY
                };
            return Ok(_repository.Tasks(definitionKeys));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,Financial Administrator")]
        [HttpGet("srn")]
        [ProducesResponseType(typeof(List<UserTaskListGetResource>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetSRNTasks()
        {
            List<string> definitionKeys = new List<string>
                {
                    SRN_APPLICATION_PROCESS_KEY,
                    SRN_UPDATE_PROCESS_KEY,
                    SRN_MERGE_SPLIT_SALE_PROCESS_KEY
                };
            return Ok(_repository.Tasks(definitionKeys));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholderadmin/{id}/member")]
        [ProducesResponseType(typeof(List<UserTaskListGetResource>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetStakeHolderAdminMemberTasks(int id)
        {
            return Ok(_repository.GetStakeHolderAdminTasks(id, MEMBER_TAKEON_PROCESS_KEY));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholderadmin/{id}/srn")]
        [ProducesResponseType(typeof(List<UserTaskListGetResource>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetStakeHolderAdminSRNTasks(int id)
        {
            return Ok(_repository.GetStakeHolderAdminTasks(id, SRN_APPLICATION_PROCESS_KEY));
        }

        [Authorize(Roles = "Financial Administrator,Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholderadmin/{id}/all")]
        [ProducesResponseType(typeof(List<UserTaskListGetResource>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetAllStakeHolderAdminTasks(int id)
        {
            return Ok(_repository.GetStakeHolderAdminTasks(id));
        }

        [Authorize(Roles = "SACRRA Administrator")]
        [HttpGet("list/sacrraadmin/{id}/srn")]
        [ProducesResponseType(typeof(List<StakeHolderManagerTaskListResource>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetSACRRAAdminSRNTasks(string id)
        {
            List<string> definitionKeys = new List<string>
                {
                    SRN_APPLICATION_PROCESS_KEY,
                    SRN_UPDATE_PROCESS_KEY,
                    SRN_MERGE_SPLIT_SALE_PROCESS_KEY,
                    SRN_STATUS_UPDATE,
                    MEMBER_STATUS_UPDATE,
                    SRN_STATUS_UPDATE_NON_CANCELLATIONS,
                    SRN_STATUS_UPDATE_TO_TEST
                };

            return Ok(_repository.GetStakeHolderManagerTasks(id, definitionKeys));
        }

        [Authorize(Roles = "Financial Administrator,Group Stakeholder Manager")]
        [HttpGet("list/financialadmin/{id}/member")]
        [ProducesResponseType(typeof(GenericTaskListResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetFinancialAdminMemberTasks(string id)
        {
            List<string> definitionKeys = new List<string>
                {
                    MEMBER_TAKEON_PROCESS_KEY,
                    MEMBER_STATUS_UPDATE
                };

            return Ok(_repository.GetFinancialAdminTasks(id, definitionKeys));
        }

        [Authorize(Roles = "Financial Administrator,Group Stakeholder Manager")]
        [HttpGet("list/financialadmin/{id}/srn")]
        [ProducesResponseType(typeof(GenericTaskListResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetFinancialAdminSRNTasks(string id)
        {
            List<string> definitionKeys = new List<string>
                {
                    SRN_APPLICATION_PROCESS_KEY,
                    SRN_STATUS_UPDATE
                };

            return Ok(_repository.GetFinancialAdminTasks(id, definitionKeys));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholdermanager/{id}/member")]
        [ProducesResponseType(typeof(StakeHolderManagerTaskListResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetStakeHolderManagerMemberTasks(string id)
        {
            List<string> definitionKeys = new List<string>
                {
                    MEMBER_TAKEON_PROCESS_KEY,
                    MEMBER_UPDATE_PROCESS_KEY
                };

            return Ok(_repository.GetStakeHolderManagerTasks(id, definitionKeys));
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpGet("list/financialadministrator/{id}/statusupdate")]
        [ProducesResponseType(typeof(StakeHolderManagerTaskListResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetFinancialAdminTasks(string id)
        {
            List<string> definitionKeys = new List<string>
                {
                    MEMBER_STATUS_UPDATE
                };

            return Ok(_repository.GetFinancialAdminTasks(id, definitionKeys));
        }

        [Authorize(Roles = "SACRRA Administrator")]
        [HttpGet("list/sacrraadministrator/{id}/statusupdate")]
        [ProducesResponseType(typeof(StakeHolderManagerTaskListResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetSacrraAdminStatusUpdateTasks(string id)
        {
            List<string> definitionKeys = new List<string>
                {
                    MEMBER_STATUS_UPDATE,
                    SRN_STATUS_UPDATE,
                    SRN_STATUS_UPDATE_NON_CANCELLATIONS,
                    SRN_STATUS_UPDATE_TO_TEST
                };

            return Ok(_repository.GetSacrraAdminTasks(id, definitionKeys));
        }

        [Authorize(Roles = "Stakeholder Manager")]
        [HttpGet("list/stakeholdermanager/{id}/statusupdate")]
        [ProducesResponseType(typeof(StakeHolderManagerTaskListResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetSHMStatusUpdateTasks(string id)
        {
            List<string> definitionKeys = new List<string>
                {
                    SRN_STATUS_UPDATE,
                    SRN_STATUS_UPDATE_TO_TEST
                };

            return Ok(_repository.GetStakeHolderManagerTasks(id, definitionKeys));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholdermanager/{id}/srn")]
        [ProducesResponseType(typeof(StakeHolderManagerTaskListResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetStakeHolderManagerSRNTasks(string id)
        {
            List<string> definitionKeys = new List<string>
                {
                    SRN_APPLICATION_PROCESS_KEY,
                    SRN_UPDATE_PROCESS_KEY,
                    SRN_MERGE_SPLIT_SALE_PROCESS_KEY,
                    SRN_STATUS_UPDATE,
                    SRN_STATUS_UPDATE_TO_TEST
                };
            return Ok(_repository.GetStakeHolderManagerTasks(id, definitionKeys));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholdermanager/dw-exceptions")]
        [ProducesResponseType(typeof(DWExceptionTaskGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetStakeHolderManagerDWExceptionTasks()
        {
            var tasks = _repository.GetDWExceptionTasks();
            return Ok(tasks);
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholdermanager/{taskId}/srn-merge")]
        [ProducesResponseType(typeof(TaskSRNMergeGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetSHMSRNMergeTask(string taskId)
        {
            return Ok(_repository.GetSHMSRNMergeTask(taskId));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholdermanager/{taskId}/srn-split")]
        [ProducesResponseType(typeof(TaskSRNSplitGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetSHMSRNSplitTask(string taskId)
        {
            return Ok(_repository.GetSHMSRNSplitTask(taskId));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholdermanager/{taskId}/srn-sale/seller")]
        [ProducesResponseType(typeof(TaskSRNSellerGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetSHMSellerSRNSaleTask(string taskId)
        {
            return Ok(_repository.GetSHMSellerSRNSaleTask(taskId));
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpGet("list/stakeholdermanager/{taskId}/srn-sale/buyer")]
        [ProducesResponseType(typeof(TaskSRNBuyerGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetSHMBuyerSRNSaleTask(string taskId)
        {
            return Ok(_repository.GetSHMBuyerSRNSaleTask(taskId));
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpPut("stakeholderadmin/{taskId}/member/allocate-shm")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteStakeHolderAdminTask(string taskId, [FromBody] TaskUpdateStakeHolderAdminResource task)
        {
            _repository.CompleteStakeHolderAdminTask(taskId, task);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/member/review")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteStakeHolderManagerTask(string taskId, [FromBody] TaskUpdateStakeHolderManagerResource task)
        {
            _repository.CompleteStakeHolderManagerReviewMemberApplicationTask(taskId, task);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/member/final-takeon")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteStakeHolderManagerFinalTakeOnTask(string taskId)
        {
            _repository.CompleteStakeHolderManagerFinalTakeOnTask(taskId);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/member/review-changes")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteStakeHolderManagerReviewMemberChangesTask(string taskId, [FromBody] TaskMemberChangesReviewResource taskChangesReviewResource)
        {
            _repository.CompleteStakeHolderManagerReviewMemberChangesTask(taskId, taskChangesReviewResource);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpPut("financialadmin/{taskId}/member/generate-full-member-invoice")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteFinancialAdminGenerateInvoiceTask(string taskId)
        {
            _repository.CompleteFinancialAdminGenerateInvoiceTask(taskId);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpPut("financialadmin/{taskId}/member/check-full-member-payment")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteFinancialAdminCheckPaymentTask(string taskId, [FromBody] TaskUpdateFinancialAdminResource task)
        {
            _repository.CompleteFinancialAdminCheckPaymentTask(taskId, task);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpPut("financialadmin/{taskId}/member/generate-assessment-invoice")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteFinancialAdminGenerateAssessmentInvoiceTask(string taskId)
        {
            _repository.CompleteFinancialAdminGenerateAssessmentInvoiceTask(taskId);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpPut("financialadmin/{taskId}/member/check-assessment-invoice-payment")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteFinancialAdminCheckAssessmentPaymentTask(string taskId, [FromBody] TaskUpdateFinancialAdminResource taskUpdateResource)
        {
            _repository.CompleteFinancialAdminCheckAssessmentInvoicePaymentTask(taskId, taskUpdateResource);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpPut("financialadmin/{taskId}/member/generate-onboarding-invoice")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteFinancialAdminGenerateOnboardingInvoiceTask(string taskId)
        {
            _repository.CompleteFinancialAdminGenerateOnboardingInvoiceTask(taskId);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator")]
        [HttpPut("financialadmin/{taskId}/member/check-onboarding-invoice-payment")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteFinancialAdminCheckOnboardingPaymentTask(string taskId, [FromBody] TaskUpdateFinancialAdminResource taskUpdateResource)
        {
            _repository.CompleteFinancialAdminCheckOnboardingInvoicePaymentTask(taskId, taskUpdateResource);
            return Ok();
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpPut("{taskId}/re-assign")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult ReAssignTask(string taskId, [FromBody] TaskUpdateResource task)
        {
            _repository.ReAssignTask(taskId, task);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/first-review")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteStakeHolderManagerSRNFirstReviewTask(string taskId, [FromBody] TaskCompleteSRNReviewResource task)
        {
            _repository.CompleteStakeHolderManagerSRNFirstReviewTask(taskId, task);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/assign/{userId}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteStakeHolderManagerSRNAssignSecondReviewerTask(string taskId, int userId)
        {
            _repository.CompleteStakeHolderManagerSRNAssignToSecondReviewerTask(taskId, userId);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/second-review")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteStakeholderManagerSecondSRNReviewTask(string taskId, [FromBody] TaskCompleteSRNReviewResource task)
        {
            _repository.CompleteStakeHolderManagerSecondSRNReviewTask(taskId, task);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/review-changes")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteStakeHolderManagerReviewSRNChangesTask(string taskId, [FromBody] TaskMemberChangesReviewResource taskChangesReviewResource)
        {
            _repository.CompleteReviewSRNChangesTask(taskId, taskChangesReviewResource);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/review-sale/seller")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteSellerStakeHolderManagerSRNReviewSRNSale(string taskId, [FromBody] TaskSellerSHMReviewSRNSale taskReview)
        {
            _repository.CompleteSellerStakeHolderManagerSRNReviewSRNSale(taskId, taskReview);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/review-sale/buyer")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteBuyerStakeHolderManagerSRNReviewSRNSale(string taskId, [FromBody] TaskBuyerSHMReviewSRNSale taskReview)
        {
            _repository.CompleteBuyerStakeHolderManagerSRNReviewSRNSale(taskId, taskReview);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/review-merge")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteStakeholderManagerSrnMergeReview(string taskId, [FromBody] TaskSHMSrnMergeReviewPutResource taskReview)
        {
            _repository.CompleteStakeholderManagerSrnMergeReview(taskId, taskReview);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/review-split")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteStakeholderManagerSrnSplitReview(string taskId, [FromBody] TaskSHMSrnSplitReviewPutResource taskReview)
        {
            _repository.CompleteStakeholderManagerSrnSplitReview(taskId, taskReview);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/confirm-testing-migration")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteBuyerStakeHolderManagerConfirmSRNTestingAndMigration(string taskId, [FromBody] TaskBuyerSHMConfirmSRNTestingMigrationResource taskReview)
        {
            _repository.CompleteBuyerStakeHolderManagerConfirmSRNTestingAndMigration(taskId, taskReview);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/dw-exceptions/complete-exception")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteStakeHolderManagerDWException(string taskId, [FromBody] TaskCompleteDWExceptionResource taskReview)
        {
            _repository.CompleteStakeHolderManagerDWException(taskId, taskReview);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/create-unconfirmed-member")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteBuyerStakeHolderManagerCreateUnconfirmedBuyer(string taskId)
        {
            _repository.CompleteBuyerStakeHolderManagerCreateUnconfirmedBuyer(taskId);
            return Ok();
        }

        [Authorize(Roles = "SACRRA Administrator, Stakeholder Manager, Financial Administrator")]
        [HttpPut("sacrraadmin/{taskId}/srn/takeon-updatedth")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteSACRRAAdminSRNTakeonUpdateDTHTask(string taskId)
        {
            _repository.CompleteSACRRAAdminSRNTakeOnUpdateDTHTask(taskId);
            return Ok();
        }

        [Authorize(Roles = "SACRRA Administrator")]
        [HttpPut("sacrraadmin/{taskId}/srn/test-added-dth")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteSACRRAAdminSRNTestAddedToDTH(string taskId)
        {
            _repository.CompleteSACRRAAdminSRNTestAddedToDTH(taskId);
            return Ok();
        }

        [Authorize(Roles = "SACRRA Administrator")]
        [HttpPut("sacrraadmin/{taskId}/srn/review-changes")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteSACRRAAdminReviewSRNChangesTask(string taskId, [FromBody] TaskMemberChangesReviewResource taskChangesReviewResource)
        {
            _repository.CompleteReviewSRNChangesTask(taskId, taskChangesReviewResource);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/application/confirm-testing-migration")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteBuyerStakeHolderManagerSRNApplication_ConfirmSRNTesting(string taskId, [FromBody] TaskConfirmSRNTestingCompleteResource confirmation)
        {
            _repository.CompleteBuyerStakeHolderManagerSRNApplication_ConfirmSRNTesting(taskId, confirmation);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/application/confirm-go-live")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteStakeHolderManagerSRNApplication_ConfirmSRNGoLive(string taskId, [FromBody] TaskConfirmSRNGoLiveResource confirmation)
        {
            _repository.CompleteStakeHolderManagerSRNApplication_ConfirmSRNGoLive(taskId, confirmation);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/split-merge-sale/confirm-go-live")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteStakeHolderManagerSRNSaleSplitMerge_ConfirmSRNGoLive(string taskId, [FromBody] TaskConfirmSRNGoLiveResource confirmation)
        {
            _repository.CompleteStakeHolderManagerSRNSaleSplitMerge_ConfirmSRNGoLive(taskId, confirmation);
            return Ok();
        }

        [Authorize(Roles = "SACRRA Administrator")]
        [HttpPut("sacrraadmin/{taskId}/srn/sale-split-merge/confirm-updated-dth")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteSACRRAAdminSRNSaleSplitMergeUpdateDTHTask(string taskId)
        {
            _repository.CompleteSACRRAAdminSRNSaleSplitMergeUpdateDTHTask(taskId);
            return Ok();
        }

        [Authorize(Roles = "Stakeholder Manager")]
        [HttpPut("stakeholdermanager/{taskId}/srn/status-update/confirm-file-submission")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteStakeholderManagerConfirmSRNFileSubmission(string taskId)
        {
            _repository.CompleteStakeholderManagerConfirmSRNFileSubmission(taskId);
            return Ok();
        }

        [Authorize(Roles = "SACRRA Administrator")]
        [HttpPut("sacrraadmin/{taskId}/srn/status-update/confirm-updated-dth")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CompleteSACRRAAdminSRNStatusChangedConfirmUpdateDTHTask(string taskId)
        {
            _repository.CompleteSACRRAAdminSRNStatusChangedConfirmUpdateDTHTask(taskId);
            return Ok();
        }

        // [Authorize(Roles = "SACRRA Administrator")]
        [HttpPost("excute-timer-events")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult ExecuteTimerEvents([FromBody]List<string> processInstanceIds)
        {
            _repository.ExecuteTimerEvents(processInstanceIds);
            return Ok();
        }
    }
}
