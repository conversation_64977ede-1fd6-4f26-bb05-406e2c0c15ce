using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Api.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Business.Resources.SRNStatus;
using System.Collections.Generic;

using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api.Controllers
{
    [Produces("application/json")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class SRNStatusesController : Controller
    {
        private readonly SRNStatusRepository _repository;

        public SRNStatusesController(SRNStatusRepository SRNStatusRepository)
        {
            _repository = SRNStatusRepository;
        }

        [Authorize]
        [HttpGet("{id}", Name = "GetSRNStatus")]
        [ProducesResponseType(typeof(SRNStatusGetResource), 200)]
        [ProducesResponseType(typeof(SRNStatusGetResource), 404)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult Get(int id)
        {
            var entity = _repository.Get(id);
            if (entity == null)
                return NotFound();

            return Ok(entity);
        }

        [Authorize]
        [HttpGet("by-name")]
        [ProducesResponseType(typeof(SRNStatusGetResource), 200)]
        [ProducesResponseType(typeof(SRNStatusGetResource), 404)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetByName(string name)
        {
            var entity = _repository.GetByName(name);
            if (entity == null)
                return NotFound();

            return Ok(entity);
        }
        [Authorize]
        [HttpGet("by-code")]
        [ProducesResponseType(typeof(SRNStatusGetResource), 200)]
        [ProducesResponseType(typeof(SRNStatusGetResource), 404)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetByCode(string code)
        {
            var entity = _repository.GetByCode(code);
            if (entity == null)
                return NotFound();

            return Ok(entity);
        }

        [Authorize]
        [HttpGet]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult List([FromQuery]NameListParams listParams, bool? isActivityAllowedWhileInProcess = null)
        {
            var SRNStatus = _repository.List(listParams, isActivityAllowedWhileInProcess);

            Response.AddPagination(SRNStatus.CurrentPage, SRNStatus.PageSize, SRNStatus.TotalCount, SRNStatus.TotalPages);

            return Ok(SRNStatus);
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpPost]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult Create([FromBody]SRNStatusCreateResource modelForCreate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var id = _repository.Create(modelForCreate);

            return CreatedAtRoute("GetSRNStatus", new { controller = "SRNStatus", id }, id);
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpPut("{id}")]
        [ProducesResponseType(typeof(SRNStatusGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult Update(int id, [FromBody]SRNStatusUpdateResource modelForUpdate)
        {
            // validate request
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            return Ok(_repository.Update(modelForUpdate));
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpDelete("{id}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult Delete(int id)
        {
            _repository.Delete(id);
            return Ok();
        }
    }
}
