using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.Auth;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api.Controllers
{
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class AuthController: Controller
    {
        private readonly AuthRepository _repository;
        private readonly ConfigSettings _configSettings;

        public AuthController(AuthRepository repository, IOptions<ConfigSettings> configSettings)
        {
            _repository = repository;
            _configSettings = configSettings.Value;
        }

        [HttpPost("login")]
        [ProducesResponseType(typeof(AuthUserResource), 200)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> Login([FromBody]AuthUserSigninResource AuthUserSigninResource)
        {
            if (!ModelState.IsValid)
                throw new InvalidUserException();

            var signedInUser = await _repository.Signin(AuthUserSigninResource);

            return Ok(signedInUser);
        }

        [HttpPost("signup")]
        [ProducesResponseType(typeof(string), 200)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> Signup([FromBody]UserCreateSimpleResource signupUser)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var createdUser = await _repository.Signup(signupUser);

            return Ok(createdUser);
        }
    
        [Authorize(Roles = "System Administrator")]
        [HttpPut("/auth0/signup-internal-users")]
        [ProducesResponseType(typeof(string), 200)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> SignupInternalUsers()
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            await _repository.CreateInternalUsersInAuth0();

            return Ok();
        }

        [HttpPut("post-change-password-hook")]
        [ProducesResponseType(typeof(string), 200)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> PostChangePasswordAuth0Hook(Auth0PostChangePasswordResource postChangePassword)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            await _repository.PostChangePasswordAuth0Hook(postChangePassword);

            return Ok();
        }
    }
}
