using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api.Controllers
{
    [Authorize(Roles = "Financial Administrator,User,Member,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,ALG Leader,Bureau")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class LookupsController: ControllerBase
    {
        private LookupsRepository _lookupsRepo;

        public LookupsController(LookupsRepository lookupsRepo)
        {
            _lookupsRepo = lookupsRepo;
        }

        [HttpGet]
        [Route("cronitor-rumkey")]
        [AllowAnonymous]
        public IActionResult GetCronitorRumKey()
        {
            var rumKey = _lookupsRepo.GetCronitorRumKey();
           
            return Ok(new { RumKey = rumKey });
        }
        
        [HttpGet]
        [Route("enums/applicationstatuses")]
        public List<IdValuePairResource> GetApplicationStatuses()
        {
            return _lookupsRepo.GetEnumIdValuePairs<ApplicationStatuses>();
        }

        [HttpGet]
        [Route("enums/getupdatememberstatuses")]
        public List<IdValuePairResource> GetUpdateMemberStatuses()
        {
            return _lookupsRepo.GetEnumIdValuePairs<ApplicationStatuses>().Where(x => x.Id == 14 || x.Id == 15).ToList();
        }

        [HttpGet]
        [Route("enums/membershiptypes")]
        public async Task<List<IdValuePairResource>> GetMembershipTypes()
        {
            return await _lookupsRepo.GetMemberTypesByUserRole<MembershipTypes>(User);
        }

        [HttpGet]
        [Route("enums/membershiptypes/all")]
        public List<IdValuePairResource> GetAllMembershipTypes()
        {
            return  _lookupsRepo.GetEnumIdValuePairs<MembershipTypes>();
        }

        [HttpGet]
        [Route("enums/principledebtranges")]
        public List<IdValuePairResource> GetPrincipleDebtRanges()
        {
            return _lookupsRepo.GetEnumIdValuePairs<PrincipleDebtRanges>();
        }

        [HttpGet]
        [Route("enums/industryclassifications")]
        public List<IdValuePairResource> GetIndustryClassifications()
        {
            return _lookupsRepo.GetEnumIdValuePairs<IndustryClassifications>();
        }

        [HttpGet]
        [Route("enums/ncrreportingprimarybusinessclassifications")]
        public List<IdValuePairResource> GetNcrReportingPrimaryBusinessClassifications()
        {
            return _lookupsRepo.GetEnumIdValuePairs<NcrReportingPrimaryBusinessClassifications>();
        }

        [HttpGet]
        [Route("enums/seller-srn-sale-review-decisions")]
        public List<IdValuePairResource> GetSellerSRNSaleReviewDecisions()
        {
            return _lookupsRepo.GetEnumIdValuePairs<SellerSRNSaleReviewDecision>();
        }

        [HttpGet]
        [Route("enums/srn-request-types")]
        public List<IdValuePairResource> GetSRNRequestTypes()
        {
            return _lookupsRepo.GetEnumIdValuePairs<SRNRequestType>();
        }

        [HttpGet]
        [Route("enums/srn-sale-types")]
        public List<IdValuePairResource> GetSRNSaleTypes()
        {
            return _lookupsRepo.GetEnumIdValuePairs<SRNSaleType>();
        }

        [HttpGet]
        [Route("enums/srn-split-types")]
        public List<IdValuePairResource> GetSRNSplitTypes()
        {
            return _lookupsRepo.GetEnumIdValuePairs<SRNSplitType>();
        }

        [HttpGet]
        [Route("enums/event-log-change-types")]
        public List<IdValuePairResource> GetEventLogChangeTypes()
        {
            return _lookupsRepo.GetEnumIdValuePairs<ChangeType>();
        }

        [HttpGet]
        [Route("enums/srn-review-merge-split-options")]
        public List<IdValuePairResource> GetSRNReviewMergeSplitOptions()
        {
            return _lookupsRepo.GetEnumIdValuePairs<SRNMergeSplitRequestReviewEnum>();
        }

        [HttpGet]
        [Route("enums/srn-review-sale-buyer-compatible-options")]
        public List<IdValuePairResource> GetSRNReviewSaleBuyerCompatibleOptions()
        {
            return _lookupsRepo.GetEnumIdValuePairs<CompatibleSRNOptions>();
        }

        [HttpGet]
        [Route("enums/userroles")]
        public List<IdValuePairResource> GetUserRoles()
        {
            return _lookupsRepo.GetEnumIdValuePairs<UserRoles>();
        }

        [HttpGet]
        [Route("enums/srn-confirm-testing-options")]
        public List<IdValuePairResource> GetSRNConfirmTestingOptions()
        {
            return _lookupsRepo.GetEnumIdValuePairs<TaskSRNApplicationConfirmSRNTestingEnum>();
        }

        [HttpGet]
        [Route("enums/srnstatustypes")]
        public List<IdValuePairResource> GetSRNStatusTypes()
        {
            return _lookupsRepo.GetEnumIdValuePairs<SRNStatusTypes>();
        }

        [HttpGet]
        [Route("enums/srnstatusfiletypes")]
        public List<IdValuePairResource> GetSRNStatusFileTypes()
        {
            return _lookupsRepo.GetEnumIdValuePairs<SRNStatusFileTypes>();
        }

        [HttpGet]
        [Route("enums/get-member-status-reasons")]
        public async Task<IActionResult> GetMemberStatusReasons()
        {
            return Ok(await _lookupsRepo.GetMemberStatusReasons());
        }
        
        [HttpGet]
        [Route("enums/get-srn-status-reasons")]
        public async Task<IActionResult> GetSRNStatusReasons()
        {
            return Ok(await _lookupsRepo.GetSRNStatusReasons());
        }

        [HttpGet]
        [Route("enums/ncrcategories")]
        public List<IdValuePairResource> GetNCRCategories()
        {
            return _lookupsRepo.GetEnumIdValuePairs<NCRCategoryEnum>();
        }

        [HttpGet]
        [Route("enums/shm-member-review-decisions")]
        public List<IdValuePairResource> GetSHMMemberReviewDecisions()
        {
            return _lookupsRepo.GetEnumIdValuePairs<MemberReviewDecisionEnum>();
        }

        [HttpGet]
        [Route("enums/member-application-payment-options")]
        public List<IdValuePairResource> GetMemberApplicationPaymentOptions()
        {
            return _lookupsRepo.GetEnumIdValuePairs<MemberApplicationPaymentEnum>();
        }

        [HttpGet]
        [Route("enums/get-srn-verification-options")]
        public List<IdValuePairResource> GetSRNVerifiedOptions()
        {
            return _lookupsRepo.GetEnumIdValuePairs<SRNVerifiedOptions>();
        }

        [HttpGet]
        [Route("enums/get-member-update-review-options")]
        public List<IdValuePairResource> GetMemberUpdateReviewOptions()
        {
            return _lookupsRepo.GetEnumIdValuePairs<MemberUpdateReviewOptions>();
        }

        [HttpGet]
        [Route("enums/srn-update-details-review-options")]
        public List<IdValuePairResource> GetSRNUpdateDetailsReviewOptions()
        {
            return _lookupsRepo.GetEnumIdValuePairs<SRNUpdateDetailsReviewOptions>();
        }

        [HttpGet]
        [Route("enums/member-auto-close-shm-options")]
        public List<IdValuePairResource> GetMemberAutoCloseSHMTaskOptions()
        {
            return _lookupsRepo.GetEnumIdValuePairs<MemberAutoCloseSHMTaskOptionsEnum>();
        }
    }
}
