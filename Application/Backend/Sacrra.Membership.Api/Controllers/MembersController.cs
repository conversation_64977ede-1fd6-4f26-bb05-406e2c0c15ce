using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Api.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Business.Resources.Member;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Business.Resources.MemberChanges;
using Sacrra.Membership.Business.Resources.MemberContact;
using Sacrra.Membership.Database.Enums;
using System.Collections.Generic;

using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api.Controllers
{
    [Authorize(Roles = "Financial Administrator,User,Member,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,Bureau,ALG Leader")]
    [Produces("application/json")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class MembersController: Controller
    {
        private readonly MemberRepository _repository;

        public MembersController(MemberRepository MemberRepository)
        {
            _repository = MemberRepository;
        }

        #region Full & Non Member Methods

        [HttpGet("{id}", Name = "GetMember")]
        [ProducesResponseType(typeof(MemberGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult Get(int id)
        {
            var data = _repository.Get(id);
            if (data == null)
                return NotFound();

            return Ok(data);
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult List([FromQuery]NameListParams listParams, ApplicationStatuses? status = null)
        {
            var Members = _repository.List(listParams, status);

            Response.AddPagination(Members.CurrentPage, Members.PageSize, Members.TotalCount, Members.TotalPages);

            return Ok(Members);
        }

        [HttpGet("all-details")]
        [ProducesResponseType(type: typeof(List<MemberGetCustomResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult ListAllDetails([FromQuery]NameListParams listParams, ApplicationStatuses? status = null)
        {
            return Ok(_repository.ListAllDetails(listParams, status));
        }

        [HttpGet("bureaus")]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetBureaus()
        {
            var bureaus = _repository.GetBureaus();
            return Ok(bureaus);
        }

        [HttpPost]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult Create([FromBody]MemberCreateResource modelForCreate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var id = _repository.Create(modelForCreate);
            return CreatedAtRoute("GetMember", new { controller = "Members", id }, id);
        }

        [HttpPost("partial")]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult CreatePartial([FromBody]MemberCreateResource modelForCreate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var id = _repository.CreatePartial(modelForCreate);
            return CreatedAtRoute("GetMember", new { controller = "Members", id }, id);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(typeof(MemberGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult UpdateMember(int id, [FromBody]MemberUpdateResource modelForUpdate)
        {
            // validate request
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            return Ok(_repository.UpdateMember(id, modelForUpdate));
        }

        [Authorize(Roles = "Group Stakeholder Manager,Financial Administrator,Stakeholder Manager,SACRRA Administrator")]
        [HttpPut("updatememberstatus/{id}")]
        [ProducesResponseType(typeof(MemberGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult UpdateMemberStatus(int id, [FromBody]MemberStatusUpdateResource modelForUpdate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            _repository.UpdateMemberStatus(id, modelForUpdate);

            return Ok();
        }

        [Authorize(Roles = "System Administrator")]
        [HttpDelete("{id}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult Delete(int id)
        {
            _repository.Delete(id);
            return Ok();
        }

        [HttpGet("{id}/tradingnames")]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult TradingNames(int id, [FromQuery]NameListParams listParams)
        {
            var users = _repository.TradingNames(id, listParams);

            Response.AddPagination(users.CurrentPage, users.PageSize, users.TotalCount, users.TotalPages);

            return Ok(users);
        }

        [HttpGet("partial")]
        [ProducesResponseType(typeof(MemberGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetPartial(int userId, bool isComplete)
        {
            return Ok(_repository.GetPartial(userId, isComplete));
        }

        [HttpGet("{id}/contacts")]
        [ProducesResponseType(type: typeof(List<MemberContactGetResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult Contacts(int id, bool memberOnly = false, bool srnOnly = false)
        {
            return Ok(_repository.Contacts(id, memberOnly, srnOnly));
        }

        [HttpPost("changelog")]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult LogChanges([FromBody]List<MemberApplicationChangePostResource> changeResourceList)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            return Ok(_repository.LogChanges(changeResourceList));
        }

        [HttpGet("changelog/list/{userId}")]
        [ProducesResponseType(typeof(List<MemberApplicationChangeGetResource>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult ListLogChanges(int userId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            return Ok(_repository.ListLogChange(userId));
        }

        [HttpPut("contacts/{id}")]
        [ProducesResponseType(typeof(MemberContactGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult UpdateContact(int id, [FromBody]MemberContactUpdateResource modelForUpdate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            return Ok(_repository.UpdateContact(id, modelForUpdate));
        }

        [HttpPut("tradingnames/{id}")]
        [ProducesResponseType(typeof(ContactTypeGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult UpdateTradingName(int id, [FromBody]TradingNameUpdateResource modelForUpdate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            return Ok(_repository.UpdateTradingName(id, modelForUpdate));
        }

        [HttpGet("{id}/change-request")]
        [ProducesResponseType(typeof(MemberStagingChangeLogResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetMemberStagingChangeRequest(int id)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            return Ok(_repository.GetStagingChangeRequest(id));
        }

        [HttpGet("reg-numbers")]
        [ProducesResponseType(typeof(List<string>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetAllCompanyRegistrationNumbers()
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            return Ok(_repository.GetAllCompanyRegistrationNumbers());
        }

        [HttpGet("event-log")]
        [ProducesResponseType(type: typeof(List<EventLogGetResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetEventLog([FromQuery]EventLogFilterResource filter)
        {
            var eventLog =  _repository.GetEventLog(filter, User);

            Response.AddPagination(eventLog.CurrentPage, eventLog.PageSize, eventLog.TotalCount, eventLog.TotalPages);

            return Ok(eventLog);
        }

        [HttpGet("event-log/getdifference/{auditHistoryId}")]
        [ProducesResponseType(type: typeof(List<StagingChange>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetEventLogDifference(int auditHistoryId)
        {
            var eventLogDifference = _repository.GetEventLogDifference(auditHistoryId);

            return Ok(eventLogDifference);
        }

        [HttpGet("{id}/id-document")]
        [ProducesResponseType(type: typeof(FileDownloadResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetIDDocument(int id)
        {
            return Ok(_repository.GetIDDocument(id));
        }

        [HttpGet("{id}/ncr-certificate")]
        [ProducesResponseType(type: typeof(FileDownloadResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetNCRCertificate(int id)
        {
            return Ok(_repository.GetNCRCertificate(id));
        }

        [HttpGet("event-log/{id}/id-document")]
        [ProducesResponseType(type: typeof(EventLogDocumentGetResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetIDDocumentEventLog(int id)
        {
            var eventLog = _repository.GetDocumentEventLog(id, "ID Document");

            return Ok(eventLog);
        }

        [HttpGet("event-log/{id}/ncr-certificate")]
        [ProducesResponseType(type: typeof(EventLogDocumentGetResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetNCRCertificateEventLog(int id)
        {
            var eventLog = _repository.GetDocumentEventLog(id, "NCR Certificate");

            return Ok(eventLog);
        }

        [Authorize(Roles = "ALG Leader")]
        [HttpGet("clients")]
        [ProducesResponseType(type: typeof(List<MemberGetSimpleResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult ListALGClients(ApplicationStatuses? status = null)
        {
            return Ok(_repository.ListALGClients(status));
        }

        [Authorize(Roles = "ALG Leader")]
        [HttpGet("clients/check-if-exists/{registrationNumber}/{leaderId}")]
        [ProducesResponseType(type: typeof(ALGClientValidatorResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult DoesALGClientExist(string registrationNumber, int leaderId)
        {
            return Ok(_repository.DoesALGClientExist(registrationNumber, leaderId));
        }

        [HttpGet("my-information")]
        [ProducesResponseType(type: typeof(List<MemberGetSimpleResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult Mynformation()
        {
            return Ok(_repository.MyInformation());
        }

        [HttpGet("does-exist/{registrationNumber}")]
        [ProducesResponseType(type: typeof(bool), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult DoesMemberExist(string registrationNumber)
        {
            return Ok(_repository.DoesMemberExist(registrationNumber));
        }

        #endregion

        #region Other Member Type Methods

        [HttpPost("affiliate")]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult CreateAffiliate([FromBody]AffiliateCreateResource modelForCreate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var id = _repository.CreateAffiliate(modelForCreate, User);
            return CreatedAtRoute("GetAffiliate", new { controller = "Members", id }, id);
        }

        [Authorize(Roles = "Financial Administrator, Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator")]
        [HttpPut("affiliate/{id}")]
        [ProducesResponseType(typeof(AffiliateGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult UpdateAffiliate([FromBody]AffiliateUpdateResource modelForCreate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var id = _repository.UpdateAffiliate(modelForCreate, User);
            return CreatedAtRoute("GetAffiliate", new { controller = "Members", id }, id);
        }

        [Authorize(Roles = "Financial Administrator, Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator")]
        [HttpGet("affiliate/{id}", Name = "GetAffiliate")]
        [ProducesResponseType(typeof(AffiliateGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetAffiliate(int id)
        {
            return Ok(_repository.GetAffiliate(id));
        }

        [HttpGet("alg-leaders")]
        [ProducesResponseType(typeof(List<IdValuePairResource>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetALGLeaders()
        {
            return Ok(_repository.GetALGLeaders());
        }

        [HttpGet("alg-leaders/{clientId}")]
        [ProducesResponseType(typeof(List<IdValuePairResource>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetALGLeaders(int clientId)
        {
            return Ok(_repository.GetALGLeaders(clientId));
        }

        [Authorize(Roles = "Financial Administrator, Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator")]
        [HttpPost("alg-leader")]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult CreateALGLeader([FromBody]ALGLeaderCreateResource modelForCreate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var id = _repository.CreateALGLeader(modelForCreate, User);
            return CreatedAtRoute("GetALGLeader", new { controller = "Members", id }, id);
        }

        [Authorize(Roles = "Financial Administrator, Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator")]
        [HttpPut("alg-leader/{id}")]
        [ProducesResponseType(typeof(ALGLeaderGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult UpdateALGLeader([FromBody]ALGLeaderUpdateResource modelForCreate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var id = _repository.UpdateALGLeader(modelForCreate, User);
            return CreatedAtRoute("GetALGLeader", new { controller = "Members", id }, id);
        }

        [HttpGet("alg-leader/{id}", Name = "GetALGLeader")]
        [ProducesResponseType(typeof(ALGLeaderGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetALGLeader(int id)
        {
            return Ok(_repository.GetALGLeader(id));
        }

        [Authorize(Roles = "Financial Administrator, Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator")]
        [HttpPost("bureau")]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult CreateBureau([FromBody]Business.Resources.BureauCreateResource modelForCreate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var id = _repository.CreateBureau(modelForCreate, User);
            return CreatedAtRoute("GetMember", new { controller = "Members", id }, id);
        }

        [Authorize(Roles = "Financial Administrator, Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator")]
        [HttpPut("bureau/{id}")]
        [ProducesResponseType(typeof(BureauGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult UpdateBureau([FromBody]BureauUpdateResource modelForCreate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var id = _repository.UpdateBureau(modelForCreate, User);
            return CreatedAtRoute("GetBureau", new { controller = "Members", id }, id);
        }

        [HttpGet("bureau/{id}", Name = "GetBureau")]
        [ProducesResponseType(typeof(BureauGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetBureau(int id)
        {
            return Ok(_repository.GetBureau(id));
        }

        [HttpGet("{id}/users")]
        [ProducesResponseType(typeof(List<UserGetResource>), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult GetUsers(int id)
        {
            return Ok(_repository.GetUsers(id));
        }

        #endregion
    }
}
