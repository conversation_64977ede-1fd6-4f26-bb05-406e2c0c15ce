using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Api.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Database.Enums;
using System.Collections.Generic;

using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api.Controllers
{
    [Produces("application/json")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class ContactTypesController : Controller
    {
        private readonly ContactTypeRepository _repository;

        public ContactTypesController(ContactTypeRepository ContactTypeRepository)
        {
            _repository = ContactTypeRepository;
        }

        [Authorize]
        [HttpGet("{id}", Name = "GetContactType")]
        [ProducesResponseType(typeof(ContactTypeGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult Get(int id)
        {
            return Ok(_repository.Get(id));
        }

        [Authorize]
        [HttpGet]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult List([FromQuery]NameListParams listParams)
        {
            var ContactTypes = _repository.List(listParams);

            Response.AddPagination(ContactTypes.CurrentPage, ContactTypes.PageSize, ContactTypes.TotalCount, ContactTypes.TotalPages);

            return Ok(ContactTypes);
        }

        [Authorize]
        [HttpGet("by-type")]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult List(string requestType, MembershipTypes membershipTypeId)
        {
            var contactTypes = _repository.List(requestType, membershipTypeId);

            return Ok(contactTypes);
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpPost]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult Create([FromBody]ContactTypeCreateResource modelForCreate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var id = _repository.Create(modelForCreate);

            return CreatedAtRoute("GetContactType", new { controller = "ContactTypes", id }, id);
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpPut("{id}")]
        [ProducesResponseType(typeof(ContactTypeGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public IActionResult Update(int id, [FromBody]ContactTypeUpdateResource modelForUpdate)
        {
            // validate request
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            return Ok(_repository.Update(modelForUpdate));
        }

        [Authorize(Roles = "Group Stakeholder Manager")]
        [HttpDelete("{id}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult Delete(int id)
        {
            _repository.Delete(id);
            return Ok();
        }
    }
}
