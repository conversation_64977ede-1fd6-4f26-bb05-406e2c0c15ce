using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Api.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources.BranchLocation;
using Sacrra.Membership.Business.Resources.IdValuePair;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api.Controllers
{
    [Authorize(Roles = "Member,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,ALG Leader")]
    [Produces("application/json")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class BranchLocationsController : Controller
    {
        private readonly BranchLocationRepository _repository;

        public BranchLocationsController(BranchLocationRepository BranchLocationRepository)
        {
            _repository = BranchLocationRepository;
        }

        [HttpGet("{id}", Name = "GetBranchLocation")]
        [ProducesResponseType(typeof(BranchLocationGetResource), 200)]
        [ProducesResponseType(typeof(BranchLocationGetResource), 404)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> Get(int id)
        {
            var entity = await _repository.Get(id);
            if (entity == null)
                return NotFound();

            return Ok(entity);
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> List([FromQuery]NameListParams listParams)
        {
            var BranchLocations = await _repository.List(listParams);

            Response.AddPagination(BranchLocations.CurrentPage, BranchLocations.PageSize, BranchLocations.TotalCount, BranchLocations.TotalPages);

            return Ok(BranchLocations);
        }

        [HttpPost]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> Create([FromBody]BranchLocationCreateResource modelForCreate)
        {
            var id = await _repository.Create(modelForCreate);

            return CreatedAtRoute("GetBranchLocation", new { controller = "BranchLocations", id }, id);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(typeof(BranchLocationGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> Update(int id, [FromBody]BranchLocationUpdateResource modelForUpdate)
        {
            // validate request
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            return Ok(await _repository.Update(modelForUpdate));
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> Delete(int id)
        {
            await _repository.Delete(id);
            return Ok();
        }
    }
}
