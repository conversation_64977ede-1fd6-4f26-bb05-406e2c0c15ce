using Camunda.Api.Client;
using Camunda.Api.Client.Deployment;
using Camunda.Api.Client.ExternalTask;
using Camunda.Api.Client.Group;
using Camunda.Api.Client.User;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources.Camunda;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Resources.Camunda.Deployment;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Camunda.CamundaAgents;

namespace Sacrra.Membership.Camunda
{
    public class CamundaAgent
    {
        private const string WORKER_ID = "CSharpWorker";

        // Lock task for 10 minutes (if there is an error it will only try that task again in 10 minutes)
        private int DEFAULT_LOCK_DURATION = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "LocalDevelopment" ? 1 * 100 * 60 : 10 * 1000 * 60;

        // If no tasks check again in 10 seconds
        private const int TIME_TO_WAIT_IF_NO_TASKS = 1000 * 10;

        private readonly IServiceScopeFactory _scopeFactory;
        private readonly CamundaClient _camundaClient;
        private readonly ConfigSettings _configSettings;
        private static readonly ILogger _log = Log.ForContext<CamundaAgent>();
        private readonly AdHocFileSubmissionsCamundaAgent _adHocFileSubmissionsCamundaAgent;
        private readonly MemberAutoCloseOnSRNClosureCamundaAgent _memberAutoCloseOnSrnClosureCamundaAgent;
        private readonly MemberStatusUpdateCamundaAgent _memberStatusUpdateCamundaAgent;
        private readonly MemberUpdateDetailsCamundaAgent _memberUpdateDetailsCamundaAgent;
        private readonly NewMemberTakeonCamundaAgent _newMemberTakeonCamundaAgent;
        private readonly ReplacementFileSubmissionCamundaAgent _replacementFileSubmissionCamundaAgent;
        private readonly NonMemberTakeonCamundaAgent _nonMemberTakeonCamundaAgent;
        private readonly SrnSellingCamundaAgent _srnSellingCamundaAgent;
        private readonly SrnStatusUpdateNonCancellationsCamundaAgent _srnStatusUpdateNonCancellationsCamundaAgent;
        private readonly SrnStatusUpdateToTestCamundaAgent _srnStatusUpdateToTestCamundaAgent;
        private readonly SrnUpdateDetailsCamundaAgent _srnUpdateDetailsCamundaAgent;
        private readonly NewSrnApplicationCamundaAgent _newSrnApplicationCamundaAgent;
        private readonly SrnStatusUpdateCamundaAgent _srnStatusUpdateCamundaAgent;

        public CamundaAgent(IOptions<ConfigSettings> configSettings, IServiceScopeFactory scopeFactory)
        {
            _scopeFactory = scopeFactory;
            var httpClient = new HttpClient();
            _configSettings = configSettings.Value;
            httpClient.BaseAddress = new Uri(_configSettings.CamundaBaseAddress);
            _camundaClient = CamundaClient.Create(httpClient);
            _adHocFileSubmissionsCamundaAgent = new AdHocFileSubmissionsCamundaAgent(_camundaClient);
            _memberAutoCloseOnSrnClosureCamundaAgent = new MemberAutoCloseOnSRNClosureCamundaAgent(_camundaClient);
            _memberStatusUpdateCamundaAgent = new MemberStatusUpdateCamundaAgent(_camundaClient);
            _memberUpdateDetailsCamundaAgent = new MemberUpdateDetailsCamundaAgent(_camundaClient);
            _newMemberTakeonCamundaAgent = new NewMemberTakeonCamundaAgent(_camundaClient);
            _replacementFileSubmissionCamundaAgent = new ReplacementFileSubmissionCamundaAgent(_camundaClient);
            _nonMemberTakeonCamundaAgent = new NonMemberTakeonCamundaAgent(_camundaClient);
            _srnSellingCamundaAgent = new SrnSellingCamundaAgent(_camundaClient);
            _srnStatusUpdateNonCancellationsCamundaAgent = new SrnStatusUpdateNonCancellationsCamundaAgent(_camundaClient);
            _srnStatusUpdateToTestCamundaAgent = new SrnStatusUpdateToTestCamundaAgent(_camundaClient);
            _srnUpdateDetailsCamundaAgent = new SrnUpdateDetailsCamundaAgent(_camundaClient);
            _newSrnApplicationCamundaAgent = new NewSrnApplicationCamundaAgent(_camundaClient);
            _srnStatusUpdateCamundaAgent = new SrnStatusUpdateCamundaAgent(_camundaClient);
        }

        private static CamundaRepository getCamundaRepository(IServiceScope serviceScope)
        {
            return serviceScope.ServiceProvider.GetRequiredService<CamundaRepository>();
        }

        internal void Run()
        {
            var startScope = _scopeFactory.CreateScope();
            _log.Information("Started : Clearing Camunda Users");
            ClearAllCamundaUsers();
            _log.Information("Ended : Clearing Camunda Users");

            // _log.Information("Started : Clearing Camunda Workflow Deployments");
            /*
                Uncomment this to clear all the workflow deployment
                but make sure that you don't run this on production
            */
            // ClearAllWorkflowDeployments();
            // _log.Information("Ended : Clearing Camunda Workflow Deployments");

            _log.Information("Started : Creating New Member Application Workflow Deployment");
            DeployWorkflowIfUpdated("SACRRA New Member", "new-member-takeon-V5.bpmn");
            _log.Information("Ended : Creating New Member Application Workflow Deployment");

            _log.Information("Started : Creating New SRN v3 Application Workflow Deployment");
            DeployWorkflowIfUpdated("SACRRA New SRN v3", "SRNApplicationV3.bpmn");
            _log.Information("Ended : Creating New Member v3 Application Workflow Deployment");

            _log.Information("Started : Creating SRN Update V2 Workflow Deployment");
            DeployWorkflowIfUpdated("SRN Status Update", "srn-status-update-V2.bpmn");
            DeployWorkflowIfUpdated("SRN Status Update to Test", "srn-status-update-test-V2.bpmn");
            _log.Information("Ended : Creating SRN Update V2 Workflow Deployment");

            _log.Information("Started : Creating Member Update Workflow Deployment");
            DeployWorkflowIfUpdated("SACRRA Member Update", "member-update-details-V1.bpmn");
            _log.Information("Ended : Creating Member Update Workflow Deployment");

            _log.Information("Started : Creating SRN Update Workflow Deployment");
            DeployWorkflowIfUpdated("SACRRA SRN Update", "srn-update-details-V2.bpmn");
            _log.Information("Ended : Creating SRN Update Workflow Deployment");

            _log.Information("Started : Creating Replacement File Submission Workflow Deployment");
            DeployWorkflowIfUpdated("SACRRA Replacement File Submission", "Replacement-File-Submissions-v4.bpmn");
            _log.Information("Ended : Creating Replacement File Submission Workflow ");

            _log.Information("Started : Creating adhoc File Submission Workflow Deployment");
            DeployWorkflowIfUpdated("SACRRA adhoc File Submission", "Ad-Hoc-File-Submissions-V2.bpmn");
            _log.Information("Ended : Creating adhoc File Submission Workflow ");

            _log.Information("Started : Creating SRN Merge/Split/Sell Workflow Deployment");
            DeployWorkflowIfUpdated("SRN Split/Merge/Sell", "SRN-Selling-V3.bpmn");
            _log.Information("Ended : Creating SRN Merge/Split/Sell Workflow Deployment");

            _log.Information("Started : Creating Member Status Update Workflow Deployment");
            DeployWorkflowIfUpdated("Member Status Update", "member-status-update-V1.bpmn");
            _log.Information("Ended : Creating Member Status Update Workflow Deployment");

            _log.Information("Started : Creating New Data Warehouse Workflow Deployment");
            DeployWorkflowIfUpdated("New DW Exception", "New-DW-Exception-V1.bpmn");
            _log.Information("Ended : Creating New Data Warehouse Workflow Deployment");

            _log.Information("Started : Creating Member Auto Close Workflow Deployment");
            DeployWorkflowIfUpdated("Member Auto Close on SRN Closure", "member-auto-close-on-srn-closure.bpmn");
            _log.Information("Ended : Creating Member Auto Close Workflow Deployment");

            _log.Information("Started : Creating SRN Status Update Non-Cancellations Workflow Deployment");
            DeployWorkflowIfUpdated("SRN Status Update Non-Cancellations", "srn-status-update-non-cancellations-V1.bpmn");
            _log.Information("Ended : Creating SRN Status Update Non-Cancellations Workflow Deployment");

            _log.Information("Started : Creating Camunda User Groups");
            var groups = getCamundaRepository(startScope).Groups();
            CreateUserGroups(groups);
            _log.Information("Ended : Creating Camunda User Groups");


            _log.Information("Started : Creating Camunda Group Authorizations");
            CreateGroupAuthorizations(groups);
            _log.Information("Ended : Creating Camunda User Groups");


            _log.Information("Started : Creating Camunda Users");
            var users = getCamundaRepository(startScope).GetUsersForCamunda();
            CreateUsers(users);
            _log.Information("Ended : Creating Camunda Users");


            _log.Information("Started : Adding Camunda Users to Groups");
            AddUsersToGroups(users);
            _log.Information("Ended : Adding Camunda Users to Groups");

            _log.Information("Listening for topics...");
            startScope.Dispose();

            while (true)
            {
                var unlockedExternalTasksCount = _camundaClient.ExternalTasks.Query(new ExternalTaskQuery() { NotLocked = true }).Count();

                // No external tasks sleep
                if (unlockedExternalTasksCount == 0)
                {
                    Thread.Sleep(TIME_TO_WAIT_IF_NO_TASKS);
                    continue;
                }

                // Get the unlocked tasks
                var tasks = _camundaClient.ExternalTasks.Query(new ExternalTaskQuery() { NotLocked = true }).List();

                foreach (var task in tasks)
                {
                    using var scope = _scopeFactory.CreateScope();
                    processTask(task.ProcessDefinitionKey, task.TopicName, scope);
                }
            }
        }

        private void processTask(string processDefinitionKey, string topicName, IServiceScope serviceScope)
        {
            string taskId = "";

            try
            {
                var lockedTasks = _camundaClient.ExternalTasks.FetchAndLock(new FetchExternalTasks()
                {
                    MaxTasks = 1,
                    WorkerId = WORKER_ID,
                    Topics = new List<FetchExternalTaskTopic>()
                {
                    new FetchExternalTaskTopic(topicName, DEFAULT_LOCK_DURATION)
                }
                });

                if (lockedTasks.Count == 0)
                    return;

                var lockedTask = lockedTasks[0];
                var task = _camundaClient.ExternalTasks[lockedTask.Id];
                taskId = lockedTask.Id;
                var completeExternalTask = new CompleteExternalTask() { WorkerId = WORKER_ID };

                _log.Information($"Processing Topic [{processDefinitionKey}]-[{topicName}]-[{taskId}]");
                processTopic(processDefinitionKey, topicName, task, completeExternalTask, serviceScope);
            }
            catch (SqlException ex)
            {
                _log.Error(ex.Message);

                var errorMessage = $"Affected topic: <strong>{topicName}</strong><br />Task ID: <strong>{taskId}</strong><br /><br /> {ex.Message}. {ex.StackTrace}";

                if (ex.Message != null)
                {
                    if (ex.Message.Contains("Execution Timeout") || ex.Message.Contains("A network-related or instance-specific error"))
                    {
                        SendEmail(errorMessage, serviceScope);
                    }
                }
                else if (ex.InnerException != null)
                {
                    if (ex.InnerException.Message != null)
                    {
                        if (ex.Message.Contains("Execution Timeout") || ex.Message.Contains("A network-related or instance-specific error"))
                        {
                            SendEmail(errorMessage, serviceScope);
                        }
                    }
                }

                var topic = topicName != null ? topicName : "";
                getCamundaRepository(serviceScope).LogError(ex, $"Unhandled SQL Camunda Exception on topic: [{processDefinitionKey}]-[{topicName}]-[{taskId}]");

                if (!string.IsNullOrEmpty(taskId))
                {
                    getCamundaRepository(serviceScope).HandleExternalTaskFailure(taskId, WORKER_ID, errorMessage, 3, DEFAULT_LOCK_DURATION);
                }
            }
            catch (Exception ex)
            {
                _log.Error(ex.Message);

                var topic = topicName != null ? topicName : "";
                getCamundaRepository(serviceScope).LogError(ex, $"Unhandled Camunda Exception on topic: [{processDefinitionKey}]-[{topicName}]-[{taskId}]");
            }
        }

        private void processTopic(string processDefinitionKey, string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            switch (processDefinitionKey)
            {
                case "Ad-Hoc-File-Submissions":
                    _adHocFileSubmissionsCamundaAgent.Process(topicName, task, completeExternalTask, serviceScope);
                    return;

                case "Member-Auto-Close-on-SRN-Closure":
                    _memberAutoCloseOnSrnClosureCamundaAgent.Process(topicName, task, completeExternalTask, serviceScope);
                    return;

                case "Member-Status-Update":
                    _memberStatusUpdateCamundaAgent.Process(topicName, task, completeExternalTask, serviceScope);
                    return;

                case "Member-Update-Details":
                    _memberUpdateDetailsCamundaAgent.Process(topicName, task, completeExternalTask, serviceScope);
                    return;

                case "New-Member-Takeon":
                    _newMemberTakeonCamundaAgent.Process(topicName, task, completeExternalTask, serviceScope);
                    return;

                case "Replacement-File-Submissions":
                    _replacementFileSubmissionCamundaAgent.Process(topicName, task, completeExternalTask, serviceScope);
                    return;

                case "Process_1wbsra4": // SACRRA_Non_Member_Workflow
                    _nonMemberTakeonCamundaAgent.Process(topicName, task, completeExternalTask, serviceScope);
                    return;

                case "SRN-Split-Merge-Sell":
                    _srnSellingCamundaAgent.Process(topicName, task, completeExternalTask, serviceScope);
                    return;

                case "SRN-Status-Update-Non-Cancellations":
                    _srnStatusUpdateNonCancellationsCamundaAgent.Process(topicName, task, completeExternalTask, serviceScope);
                    return;

                case "SRN-Status-Update-To-Test":
                    _srnStatusUpdateToTestCamundaAgent.Process(topicName, task, completeExternalTask, serviceScope);
                    return;

                case "SRN-Update-Details":
                    _srnUpdateDetailsCamundaAgent.Process(topicName, task, completeExternalTask, serviceScope);
                    return;

                case "New-SRN-Application":
                    _newSrnApplicationCamundaAgent.Process(topicName, task, completeExternalTask, serviceScope);
                    return;

                case "SRN-Status-Update":
                    _srnStatusUpdateCamundaAgent.Process(topicName, task, completeExternalTask, serviceScope);
                    return;
            }

            throw new NotImplementedException($"Process [{processDefinitionKey}]-[{topicName}] not implemented.");
        }

        private void DeployWorkflowIfUpdated(string deploymentName, string workflowFileName)
        {
            var currentDeploymentXMLString = File.ReadAllText($"./bpmn/{workflowFileName}");
            var previousDeploymentXMLString = GetLastDeploymentResourceXmlString(deploymentName);
            if (currentDeploymentXMLString != previousDeploymentXMLString)
            {
                using var memoryStream = new MemoryStream(Encoding.UTF8.GetBytes(currentDeploymentXMLString));
                var fileContent = new ResourceDataContent(memoryStream, workflowFileName);
                _camundaClient.Deployments.Create
                (
                    deploymentName,
                    fileContent
                );
            }
        }

        private string GetLastDeploymentResourceXmlString(string deploymentName)
        {
            using var client = new HttpClient();

            var lastDeploymentID = GetLastDeploymentID(deploymentName, client);
            if (lastDeploymentID == null)
                return null;

            var resourceID = GetResourceID(lastDeploymentID, client);
            return GetLastResourceXmlString(lastDeploymentID, resourceID, client);
        }

        private string GetLastDeploymentID(string deploymentName, HttpClient client)
        {
            var uri = _configSettings.CamundaBaseAddress +
                      $"/deployment?name={deploymentName}&sortOrder=desc&sortBy=deploymentTime";
            var result = client.Get(uri);

            result.EnsureSuccessStatusCode();

            var resultString = result.Content.ReadAsStringAsync();
            var deploymentList = JsonConvert.DeserializeObject<DeploymentGetResource[]>(resultString);
            if (deploymentList.Length == 0)
            {
                return null;
            }

            var latestDeploymentID = deploymentList[0].Id;
            return latestDeploymentID;
        }

        private string GetResourceID(string deploymentID, HttpClient client)
        {
            var uri = _configSettings.CamundaBaseAddress + $"/deployment/{deploymentID}/resources";
            var result = client.Get(uri);

            result.EnsureSuccessStatusCode();
            var resultString = result.Content.ReadAsStringAsync();
            var resourceID = JsonConvert.DeserializeObject<ResourceGetResource[]>(resultString)[0].Id;
            return resourceID;
        }

        private string GetLastResourceXmlString(string deploymentID, string resourceID,
            HttpClient client)
        {
            var uri = _configSettings.CamundaBaseAddress + $"/deployment/{deploymentID}/resources/{resourceID}/data";
            var result = client.Get(uri);

            result.EnsureSuccessStatusCode();
            var resultString = result.Content.ReadAsStringAsync();
            return resultString;
        }

        public AuthorizationGetResource CreateAuthorizations(AuthorizationCreateResource authorizationCreateResource)
        {
            var contractResolver = new DefaultContractResolver
            {
                NamingStrategy = new CamelCaseNamingStrategy()
            };

            using (var client = new HttpClient())
            {
                var json = JsonConvert.SerializeObject(authorizationCreateResource, new JsonSerializerSettings
                {
                    ContractResolver = contractResolver,
                    Formatting = Formatting.Indented
                });
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var uri = _configSettings.CamundaBaseAddress + "/authorization/create";
                var result = client.Post(uri, content);

                result.EnsureSuccessStatusCode();

                var resultString = result.Content.ReadAsStringAsync();
                var resultsObject = JsonConvert.DeserializeObject<AuthorizationGetResource>(resultString);

                return resultsObject;
            }
        }

        private void CreateUserGroups(List<string> groups)
        {
            foreach (var group in groups)
            {
                var existingGroup = _camundaClient.Group.Query(new GroupQuery { Id = group });

                if (existingGroup.Count() == 0)
                {
                    await _camundaClient.Group.Create(new GroupInfo()
                    {
                        Id = group,
                        Name = group,
                        Type = "WORKFLOW"
                    });
                }
            }
        }

        private void CreateGroupAuthorizations(List<string> groups)
        {

            foreach (var group in groups)
            {
                using (var client = new HttpClient())
                {
                    var uri = _configSettings.CamundaBaseAddress + "/authorization?groupIdIn=" + group + "&resourceType=6";
                    var result = client.Get(uri);

                    result.EnsureSuccessStatusCode();

                    var resultString = result.Content.ReadAsStringAsync();
                    var authorisations = JsonConvert.DeserializeObject<List<AuthorizationGetResource>>(resultString);

                    if (authorisations.Count == 0)
                    {
                        var authorizationCreateResource = new AuthorizationCreateResource()
                        {
                            Type = 1,
                            Permissions = new List<string>()
                                {
                                    "ALL"
                                },
                            UserId = null,
                            GroupId = group,
                            ResourceType = 6,
                            ResourceId = "*"
                        };

                        CreateAuthorizations(authorizationCreateResource);
                    }
                }
            }
        }

        private void CreateUsers(List<UserCamundaGetResource> users)
        {
            //Create default camunda admin user
            var adminUserExists = _camundaClient.Users.Query(new UserQuery { Id = _configSettings.CamundaAdminUserName });

            if (adminUserExists.Count() == 0)
            {
                var newUser = new UserProfileInfo
                {
                    Email = _configSettings.CamundaAdminEmail,
                    FirstName = "SACRRA Camunda",
                    Id = _configSettings.CamundaAdminUserName,
                    LastName = "Admin"
                };

                _camundaClient.Users.Create(newUser, _configSettings.CamundaAdminPassword);

                _camundaClient.Group["camunda-admin"].AddMember(newUser.Id.ToString());
            }

            //Create internal SACRRA users from the membership DB
            foreach (var user in users)
            {
                var userExists = _camundaClient.Users.Query(new UserQuery { Id = user.Id.ToString() });

                if (userExists.Count() == 0)
                {
                    var newUser = new UserProfileInfo
                    {
                        Email = user.Email,
                        FirstName = user.FirstName,
                        Id = user.Id.ToString(),
                        LastName = user.LastName
                    };

                    _camundaClient.Users.Create(newUser, "password");
                }
            }
        }

        private void AddUsersToGroups(List<UserCamundaGetResource> users)
        {
            foreach (var user in users)
            {
                var existingMember = _camundaClient.Group.Query(new GroupQuery { Member = user.Id.ToString() });

                if (existingMember.Count() == 0)
                {
                    switch (user.RoleId)
                    {
                        case UserRoles.FinancialAdministrator:
                            await _camundaClient.Group[UserRoles.FinancialAdministrator.ToString()].AddMember(user.Id.ToString());

                            break;

                        case UserRoles.StakeHolderAdministrator:
                            _camundaClient.Group[UserRoles.StakeHolderAdministrator.ToString()].AddMember(user.Id.ToString());
                            break;

                        case UserRoles.StakeHolderManager:
                            _camundaClient.Group[UserRoles.StakeHolderManager.ToString()].AddMember(user.Id.ToString());
                            break;
                        case UserRoles.SACRRAAdministrator:
                            _camundaClient.Group[UserRoles.SACRRAAdministrator.ToString()].AddMember(user.Id.ToString());
                            break;
                    }
                }
            }
        }

        private void ClearAllCamundaUsers()
        {
            if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "UAT" || Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "LocalDevelopment")
            {
                var users = _camundaClient.Users.Query().List();

                foreach (var user in users)
                {
                    if (user.Id.ToLower() != "sacrra")
                        _camundaClient.Users[user.Id].Delete();
                }
            }
        }

        private void SendEmail(string errorMessage, IServiceScope serviceScope)
        {
            var recipients = getCamundaRepository(serviceScope).GetCamundaErrorRecipients();

            foreach (var recipient in recipients)
            {
                string subject = "Camunda Agent Service Error";
                string messageBody = $"Hi {recipient.FirstName},<br /><br />";
                messageBody += $"The camunda agent service encountered an error. See error message below<br /><br />";

                messageBody += $"{errorMessage}";

                getCamundaRepository(serviceScope).SendEmail(recipient.Email, recipient.FirstName, subject, messageBody);
            }
        }
    }
}
