using System;
using System.Collections.Generic;
using System.Linq;

using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Sacrra.Membership.Business.DTOs.SRNUpdateDTOs;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Business.Services;

public class SrnUpdateDetailsCamundaService
{
    private readonly AppDbContext _dbContext;
    private readonly SRNServiceHelper _srnHelper;
    private readonly EmailService _emailService;

    public SrnUpdateDetailsCamundaService(AppDbContext dbContext, SRNServiceHelper srnHelper, EmailService emailService)
    {
        _dbContext = dbContext;
        _srnHelper = srnHelper;
        _emailService = emailService;
    }

    public ChangeRequestStaging GetMemberChangeRequest(int id)
    {
        try
        {
            var changeRequest = _dbContext.MemberChangeRequests
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == id);

            return changeRequest;
        }
        catch (Exception ex)
        {
            var message = "Unable to retrieve member change request for member Id " + id;
            Sacrra.Membership.Business.Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public void CamundaApplySRNChanges(int srnId, ChangeRequestStaging changeRequest)
    {
        if (srnId > 0)
        {
            if (changeRequest != null)
            {
                var modelForUpdate = JsonConvert.DeserializeObject<SRNUpdateInputDTO>(changeRequest.UpdatedDetailsBlob);

                if (modelForUpdate != null)
                {
                    _srnHelper.ApplySRNChanges(srnId, modelForUpdate,
                        new User { FirstName = "Camunda", LastName = "User" });
                }

                _dbContext.Remove(changeRequest);
                _dbContext.SaveChanges();
            }
        }
    }

    public void NotifyApplicantOfSRNUpdateAccepted(int srnId)
    {
        try
        {
            var srn = _dbContext.SRNs
                .Include(i => i.Member)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == srnId);

            if (srn != null)
            {
                if (srn.Member != null)
                {
                    var mainContact = Helpers.Helpers.GetMemberMainContact(_dbContext, srn.Member.Id);

                    if (mainContact != null)
                    {
                        var applicant = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };

                        _emailService.SendEmail(applicant.Email, applicant.FirstName, "SRN Details Update Accepted",
                            "SRNUpdateAcceptedApplicant.html", placeholders);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email applicant for SRN details update accepted. SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public void NotifySHMAndSACRRAAdminOfSRNUpdate(int srnId)
    {
        try
        {
            var srn = _dbContext.SRNs
                .Include(i => i.Member)
                .ThenInclude(i => i.StakeholderManager)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == srnId);

            var admin = _dbContext.Users
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.RoleId == UserRoles.SACRRAAdministrator);

            if (srn != null)
            {
                if (srn.Member != null)
                {
                    if (srn.Member.StakeholderManager != null)
                    {
                        var shm = srn.Member.StakeholderManager;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };

                        _emailService.SendEmail(shm.Email, shm.FirstName, "SRN Updated - No Approval Required",
                            "SRNUpdateNoApprovalSHM.html", placeholders);
                    }

                    if (admin != null)
                    {
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };

                        _emailService.SendEmail(admin.Email, admin.FirstName, "SRN Updated - No Approval Required",
                            "SRNUpdateNoApprovalSHM.html", placeholders);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email SHM for SRN update. Member Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public int GetSRNStakeholderManager(int srnId)
    {
        var srn = _dbContext.Set<SRN>()
            .Include(i => i.Member)
            .Where(i => i.Id == srnId)
            .Select(m => new SRN
            {
                Member = new()
                {
                    StakeholderManagerId = m.Member.StakeholderManagerId
                }
            })
            .FirstOrDefault();

        if (srn != null)
        {
            return (srn.Member.StakeholderManagerId != null) ? (int)srn.Member.StakeholderManagerId : 0;
        }

        return 0;
    }

    public void NotifyApplicantOfSRNUpdateDecline(int srnId)
    {
        try
        {
            var srn = _dbContext.SRNs
                .Include(i => i.Member)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == srnId);

            if (srn != null)
            {
                if (srn.Member != null)
                {
                    var mainContact = Helpers.Helpers.GetMemberMainContact(_dbContext, srn.Member.Id);

                    if (mainContact != null)
                    {
                        var applicant = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };

                        _emailService.SendEmail(applicant.Email, applicant.FirstName, "SRN Details Update Declined",
                            "SRNUpdateDeclinedApplicant.html", placeholders);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email applicant for SRN details update rejection. Member Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }
}