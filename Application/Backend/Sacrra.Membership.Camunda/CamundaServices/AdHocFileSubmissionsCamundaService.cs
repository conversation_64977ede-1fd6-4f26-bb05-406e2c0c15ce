using AutoMapper.Execution;
using Camunda.Api.Client.ExternalTask;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Renci.SshNet;
using RestSharp;
using Sacrra.Membership.Business.DTOs.DataWarehouseDTO;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Business.Services.DataWarehouseService;
using Sacrra.Membership.Business.Services.LookupsService;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace Sacrra.Membership.Business.Services;

public class AdHocFileSubmissionsCamundaService
{
    private readonly GlobalHelper _globalHelper;
    private readonly AppDbContext _dbContext;
    private readonly EmailService _emailService;
    private readonly DataWarehouseService.DataWarehouseService _dataWarehouseService;
    private readonly LookupsService.LookupsService _lookupsService;
    private readonly ConfigSettings _configSettings;

    public AdHocFileSubmissionsCamundaService(GlobalHelper globalHelper, AppDbContext dbContext,
        EmailService emailService, DataWarehouseService.DataWarehouseService dataWarehouseService,
        LookupsService.LookupsService lookupsService, IOptions<ConfigSettings> configSettings)
    {
        _globalHelper = globalHelper;
        _dbContext = dbContext;
        _emailService = emailService;
        _dataWarehouseService = dataWarehouseService;
        _lookupsService = lookupsService;
        _configSettings = configSettings.Value;
    }

    public void AdHocFileSubmissionEmailMemberBureausCancelled(string processInstanceId)
    {
        var taskVariables = _globalHelper.GetVariables(processInstanceId);
        var srnIdVariable = taskVariables.Find(x => x.Name == "SRNId")?.Value;
        var spIdVariable = taskVariables.Find(x => x.Name == "SPId")?.Value;
        long srnId = 0;
        long spId = 0;

        if (taskVariables.Find(x => x.Name == "fileSubmittedOnPlannedDate").Value == "cancelled")
        {
            if (srnIdVariable != null)
            {
                srnId = long.Parse(srnIdVariable);
            }

            if (spIdVariable != null)
            {
                spId = long.Parse(spIdVariable);
            }

            var adhocFileSubmissionId = long.Parse(taskVariables.Find(x => x.Name == "FileSubmissionRequestId").Value);
            var adhocFileSubmissionSRN = _dbContext.SRNs
                .Where(x => x.Id == srnId)
                .Include(x => x.Member)
                .Include(x => x.Contacts)
                .FirstOrDefault();
            var adhocFileSubmissionSPNumber = _dbContext.SPGroups
                .Where(x => x.Id == spId)
                .Include(x => x.Member)
                .Include(x => x.Member.Contacts)
                .FirstOrDefault();
            var adhocFileSubmission = _dbContext.AdhocFileSubmissions
                .Where(x => x.Id == adhocFileSubmissionId)
                .FirstOrDefault();
            var adhocFileSubmissionCancelReason = _dbContext.AdhocFileSubmissions
                .Where(x => x.Id == adhocFileSubmissionId)
                .FirstOrDefault()
                .ReasonForDeletion;

            var memberPlaceholders = new List<KeyValuePair<string, string>>
            {
                new KeyValuePair<string, string>("[DataContributorName]", adhocFileSubmissionSRN.Member.RegisteredName),
                new KeyValuePair<string, string>("[FileName]", adhocFileSubmission.FileName),
                new KeyValuePair<string, string>("[SRNNumber]", adhocFileSubmission.SRN.SRNNumber),
                new KeyValuePair<string, string>("[ReasonForSubmission]",
                    _dbContext.AdhocFileSubmissionReason
                        .Where(x => x.Id == adhocFileSubmission.AdhocFileSubmissionReasonId).FirstOrDefault().Name),
                new KeyValuePair<string, string>("[ProposedSubmissionDate]",
                    adhocFileSubmission.PlannedSubmissionDate.ToString()),
                new KeyValuePair<string, string>("[CancellationReason]", adhocFileSubmissionCancelReason)
            };

            MemberContact mainContact = null;
            SRNContact srnDataContact = null;

            if (adhocFileSubmissionSRN == null && adhocFileSubmissionSPNumber != null)
            {
                mainContact = adhocFileSubmissionSPNumber.Member.Contacts.FirstOrDefault(x => x.ContactTypeId == 1);
            }

            if (adhocFileSubmissionSRN != null && adhocFileSubmissionSPNumber == null)
            {
                srnDataContact = adhocFileSubmissionSRN.Contacts.FirstOrDefault(x => x.ContactTypeId == 5);
            }

            var bureauList = _dbContext.Members.Where(x => x.MembershipTypeId == MembershipTypes.Bureau).ToList();
            var bureauContactList = new List<string>();

            foreach (var bureau in bureauList)
            {
                if (bureau.RegisteredName.ToLower() == "unknown")
                {
                    continue;
                }

                var bureauContact = _dbContext.MemberContacts
                    .Where(x => x.MemberId == bureau.Id && x.ContactTypeId == 5)
                    .FirstOrDefault();

                bureauContactList.Add(bureauContact.Email);
            }

            _emailService.SendEmail(mainContact == null ? srnDataContact.Email : mainContact.Email,
                mainContact == null ? srnDataContact.FirstName : mainContact.FirstName,
                "Ad-Hoc File Submission Cancelled", "AdHocFileSubmissionCancelled.html", memberPlaceholders,
                bureauContactList, "", "",
                adhocFileSubmissionSRN == null ? adhocFileSubmissionSPNumber.Id : adhocFileSubmissionSRN.Id,
                WorkflowEnum.AdHocFileSubmissions, EmailReasonEnum.AdHocFileSubmissionCancelled,
                EmailRecipientTypeEnum.Member);
        }
    }

    public void AdHocFileSubmissionUpdateFileSubmissionToSubmittedOrCancelled(ExternalTaskInfo camundaTask)
    {
        var taskVariables = _globalHelper.GetVariables(camundaTask.ProcessInstanceId);
        int adhocFileSubmissionId;
        AdhocFileSubmission adhocFileSubmission;

        if (taskVariables != null)
        {
            adhocFileSubmissionId = int.Parse(taskVariables.Find(x => x.Name == "FileSubmissionRequestId").Value);
        }
        else
        {           
            throw new Exception(
                $"Unable to fetch variables for task with process instance ID ({camundaTask.ProcessInstanceId})");
        }

        if (adhocFileSubmissionId > 0)
        {
            adhocFileSubmission = _dbContext.AdhocFileSubmissions
                .Where(x => x.Id == adhocFileSubmissionId)
                .Include(x => x.SRN)
                .FirstOrDefault();
        }
        else
        {
            throw new Exception($"Unable to find replacement file submission with ID ({adhocFileSubmissionId})");
        }

        try
        {
            var whereClause = $"1 = 1";

            whereClause +=
                $" AND FileName = '{adhocFileSubmission.FileName}' AND FileStatus != 'FILE_VALIDATION_FAILED'";
            DataWarehouseAPIModel apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "FileName, FileStatus, TransactionDate",
                Where = whereClause
            };

            var adhocfile = _dataWarehouseService
                .GetResultArray<FileSubmissionOutputDTO>("API.vwDailyAndMonthlyFileSubmissions", apiCallModel).ToList();
            
            if (adhocfile.Count == 0)
            {
                throw new Exception("Adhoc file found in data warehouse");
            }

            if (taskVariables.Find(x => x.Name == "fileSubmittedOnPlannedDate").Value == "submitted")
            {
                adhocFileSubmission.AdhocFileSubmissionStatusId = (int)ReplacementFileSubmissionStatuses.Submitted;
                adhocFileSubmission.SubmissionStatusDate = DateTime.Now;
                adhocFileSubmission.ActualSubmissionDate = adhocfile[0].TransactionDate;
                
                Helpers.Helpers
                        .CreateEventLogSystemTask(_dbContext,  "System Task", "TaskCompletion", adhocFileSubmission.FileName, JsonConvert.SerializeObject(adhocFileSubmission),
                        "{\"Changes\":[{\"Name\":\"AdhocFileSubmissionStatusId\",\"OldValue\":\"" +
                              adhocFileSubmission.AdhocFileSubmissionStatusId.ToString() +
                              "\",\"NewValue\":\"1\"}]}"
                        , adhocFileSubmission.Id, "AdHocFileSubmission");
            }
            else
            {
                adhocFileSubmission.AdhocFileSubmissionStatusId = (int)ReplacementFileSubmissionStatuses.Cancelled;
                adhocFileSubmission.SubmissionStatusDate = DateTime.Now;
            }

            _dbContext.SaveChanges();
        }
        catch (Exception exception)
        {
            Log.Error(exception, $"Unable to update adhoc file submission with ID ({adhocFileSubmissionId})");
            throw new Exception($"Unable to update adhoc file submission with ID ({adhocFileSubmissionId})");
        }
    }

    public void AdHocFileSubmissionEmailMemberDeclined(string processInstanceId)
    {
        var taskVariables = _globalHelper.GetVariables(processInstanceId);
        var srnIdVariable = taskVariables.Find(x => x.Name == "SRNId")?.Value;
        var spIdVariable = taskVariables.Find(x => x.Name == "SPId")?.Value;
        long srnId = 0;
        long spId = 0;

        if (srnIdVariable != null)
        {
            srnId = long.Parse(srnIdVariable);
        }

        if (spIdVariable != null)
        {
            spId = long.Parse(spIdVariable);
        }

        var adhocFileSubmissionId = long.Parse(taskVariables.Find(x => x.Name == "FileSubmissionRequestId").Value);
        var adhocFileSubmissionSRN = _dbContext.SRNs
            .Where(x => x.Id == srnId)
            .Include(x => x.Member)
            .Include(x => x.Contacts)
            .FirstOrDefault();
        var adhocFileSubmissionSPNumber = _dbContext.SPGroups
            .Where(x => x.Id == spId)
            .Include(x => x.Member)
            .Include(x => x.Member.Contacts)
            .FirstOrDefault();
        var adhocFileSubmission = _dbContext.AdhocFileSubmissions
            .Where(x => x.Id == adhocFileSubmissionId)
            .FirstOrDefault();
        var adhocFileSubmissionDeclineReason = _lookupsService
            .GetEnumIdValuePairs<ReplacementFileSubmissionDeclineReasons>()
            .Find(x => x.Id == adhocFileSubmission.AdhocFileSubmissionDeclineReasonId).Value;

        var memberPlaceholders = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("[DataContributorName]", adhocFileSubmissionSRN.Member.RegisteredName),
            new KeyValuePair<string, string>("[FileName]", adhocFileSubmission.FileName),
            new KeyValuePair<string, string>("[SRNNumber]", adhocFileSubmission.SRN.SRNNumber),
            new KeyValuePair<string, string>("[ReasonForSubmission]",
                _dbContext.AdhocFileSubmissionReason.Where(x => x.Id == adhocFileSubmission.AdhocFileSubmissionReasonId)
                    .FirstOrDefault().Name),
            new KeyValuePair<string, string>("[DeclineReason]", adhocFileSubmissionDeclineReason)
        };

        MemberContact mainContact = null;
        SRNContact srnDataContact = null;

        if (adhocFileSubmissionSRN == null && adhocFileSubmissionSPNumber != null)
        {
            mainContact = adhocFileSubmissionSPNumber.Member.Contacts
                .FirstOrDefault(x => x.ContactTypeId == 1);
        }

        if (adhocFileSubmissionSRN != null && adhocFileSubmissionSPNumber == null)
        {
            srnDataContact = adhocFileSubmissionSRN.Contacts
                .FirstOrDefault(x => x.ContactTypeId == 5);
        }

        _emailService.SendEmail(mainContact == null ? srnDataContact.Email : mainContact.Email,
            mainContact == null ? srnDataContact.FirstName : mainContact.FirstName, "Ad-Hoc File Submission Declined",
            "AdHocFileSubmissionDeclined.html", memberPlaceholders, null, "", "",
            adhocFileSubmissionSRN == null ? adhocFileSubmissionSPNumber.Id : adhocFileSubmissionSRN.Id,
            WorkflowEnum.AdHocFileSubmissions, EmailReasonEnum.AdHocFileSubmissionDeclined,
            EmailRecipientTypeEnum.Member);
    }

    public void AdHocFileSubmissionCreateFileOnFTPServer(string processInstanceId)
    {
        try
        {
            var taskVariables = _globalHelper.GetVariables(processInstanceId);
            var adhocFileSubmissionId = long.Parse(taskVariables.Find(x => x.Name == "FileSubmissionRequestId").Value);
            var adhocFileSubmission = _dbContext.AdhocFileSubmissions
                .Where(x => x.Id == adhocFileSubmissionId)
                .FirstOrDefault();
            var client = new SftpClient(_configSettings.FTPServerAddress, int.Parse(_configSettings.FTPServerPort),
                _configSettings.FTPServerUser, _configSettings.FTPServerPassword);

            client.Connect();
            client.Create("./" + adhocFileSubmission.FileName);
            client.Disconnect();
            client.Dispose();
        }
        catch (Exception exception)
        {
            var message = "Unable to create file on FTP server for adhoc File Submission.";
            Helpers.Helpers.LogError(_dbContext, exception, message);
            throw new Exception(message);
        }
    }

    public void AdHocFileSubmissionEmailMemberApproved(string processInstanceId)
    {
        var taskVariables = _globalHelper.GetVariables(processInstanceId);
        var srnIdVariable = taskVariables.Find(x => x.Name == "SRNId")?.Value;
        long srnId = long.Parse(srnIdVariable);

        var adHocFileSubmissionId = long.Parse(taskVariables.Find(x => x.Name == "FileSubmissionRequestId").Value);
        var adHocFileSubmissionFileName = taskVariables.Find(x => x.Name == "AdHocFileSubmissionFileName").Value;
        var adHocFileSubmissionSRN = _dbContext.SRNs
            .Where(x => x.Id == srnId)
            .Include(x => x.Member)
            .Include(x => x.Contacts)
            .FirstOrDefault();
        var adHocFileSubmission = _dbContext.AdhocFileSubmissions
            .Where(x => x.Id == adHocFileSubmissionId)
            .Where(x => x.FileName == adHocFileSubmissionFileName)
            .Include(x => x.FileSubmissionReason)
            .FirstOrDefault();

        var memberPlaceholders = new List<KeyValuePair<string, string>>
        {
            new KeyValuePair<string, string>("[DataContributorName]", adHocFileSubmissionSRN.Member.RegisteredName),
            new KeyValuePair<string, string>("[FileName]", adHocFileSubmission.FileName),
            new KeyValuePair<string, string>("[SRNNumber]", adHocFileSubmission.SRN.SRNNumber),
            new KeyValuePair<string, string>("[SRNDisplayName]", adHocFileSubmission.SRN.TradingName),
            new KeyValuePair<string, string>("[ReasonForSubmission]",
                _dbContext.AdhocFileSubmissionReason.Where(x => x.Id == adHocFileSubmission.AdhocFileSubmissionReasonId)
                    .FirstOrDefault().Name),
            new KeyValuePair<string, string>("[ProposedSubmissionDate]",
                adHocFileSubmission.PlannedSubmissionDate.ToString()),
            new KeyValuePair<string, string>("[NumberOfRecords]", adHocFileSubmission.NumberOfRecords.ToString()),
            new KeyValuePair<string, string>("[Comments]", adHocFileSubmission.Comments)
        };

        var srnDataContact = adHocFileSubmissionSRN.Contacts
            .FirstOrDefault(x => x.ContactTypeId == 5);
        var bureauList = _dbContext.Members.Where(x => x.MembershipTypeId == MembershipTypes.Bureau).ToList();
        var bureauContactList = new List<string>();

        foreach (var bureau in bureauList)
        {
            if (bureau.RegisteredName.ToLower() == "unknown")
            {
                continue;
            }

            var bureauContact = _dbContext.MemberContacts
                .Where(x => x.MemberId == bureau.Id && x.ContactTypeId == 5)
                .FirstOrDefault();

            bureauContactList.Add(bureauContact.Email);
        }
        
        _emailService.SendEmail(srnDataContact.Email, srnDataContact.FirstName, "Ad-Hoc File Submission Approved",
            "AdHocFileSubmissionApproved.html", memberPlaceholders, bureauContactList, "", "",
            adHocFileSubmissionSRN.Id, WorkflowEnum.AdHocFileSubmissions,
            EmailReasonEnum.AdHocFileSubmissionApproved, EmailRecipientTypeEnum.Member);
    }

    public void UpdateAdHocFileSubmissionStatusToDeclined(string processInstanceId)
    {
        var taskVariables = _globalHelper.GetVariables(processInstanceId);
        int adHocFileSubmissionId;
        AdhocFileSubmission adhocFileSubmission;

        if (taskVariables != null)
        {
            adHocFileSubmissionId = int.Parse(taskVariables.Find(x => x.Name == "FileSubmissionRequestId").Value);
        }
        else
        {
            throw new Exception($"Unable to fetch variables for task with process instance ID ({processInstanceId})");
        }

        if (adHocFileSubmissionId > 0)
        {
            adhocFileSubmission = _dbContext.AdhocFileSubmissions
                .Where(x => x.Id == adHocFileSubmissionId)
                .FirstOrDefault();
        }
        else
        {
            throw new Exception($"Unable to find replacement file submission with ID ({adHocFileSubmissionId})");
        }

        try
        {
            adhocFileSubmission.AdhocFileSubmissionStatusId = (int)ReplacementFileSubmissionStatuses.Declined;
            adhocFileSubmission.AdhocFileSubmissionDeclineReasonId =
                long.Parse(taskVariables.Find(x => x.Name == "reasonForDecline")?.Value);
            _dbContext.AdhocFileSubmissions.Update(adhocFileSubmission);
            _dbContext.SaveChanges();
        }
        catch
        {
            throw new Exception($"Unable to update replacement file submission with ID ({adHocFileSubmissionId})");
        }
    }

    public void AdhocFileSubmissionsDueRecheck()
    {
        try
        {
            var restClient = new RestClient();
            var taskKey = "Task_1nyudk8";
            var taskUrl = $"{_configSettings.CamundaBaseAddress}/task?taskDefinitionKey={taskKey}";

            var getRequest = new RestRequest(taskUrl, Method.Get);
            var getResponse = restClient.Execute(getRequest);

            if (!getResponse.IsSuccessful)
            {
                throw new Exception($"Failed to fetch tasks from Camunda. Status: {getResponse.StatusCode}, Content: {getResponse.Content}");
            }

            var taskList = JsonConvert.DeserializeObject<CamundaTaskDTO[]>(getResponse.Content);

            foreach (var task in taskList ?? Enumerable.Empty<CamundaTaskDTO>())
            {
                Console.WriteLine($"Completing task with ID: {task.Id}");

                var taskVariables = new
                {
                    variables = new
                    {
                        recheckDthSubmission = new { value = true, type = "boolean" }
                    }
                };

                var completeUrl = $"{_configSettings.CamundaBaseAddress}/task/{task.Id}/complete";
                var completeRequest = new RestRequest(completeUrl, Method.Post)
                    .AddJsonBody(taskVariables);

                var completeResponse = restClient.Execute(completeRequest);

                if (!completeResponse.IsSuccessful)
                {
                    _globalHelper.LogError(_dbContext, null, $"Failed to complete task ID: {task.Id}. Status: {completeResponse.StatusCode}, Content: {completeResponse.Content}");
                }
            }
        }
        catch (Exception ex)
        {
            _globalHelper.LogError(_dbContext, ex, "Unable to complete tasks to check if replacement file was submitted to DTH.");
            throw;
        }
    }

    public void AdHocFileSubmissionCheckFileSubmittedToDTH(ExternalTaskInfo task)
    {
        var taskVariables = _globalHelper.GetVariables(task.ProcessInstanceId);
        var adhocFileSubmissionId = long.Parse(taskVariables.Find(x => x.Name == "FileSubmissionRequestId").Value);
        var adhocFileSubmission = _dbContext.AdhocFileSubmissions
            .Where(x => x.Id == adhocFileSubmissionId)
            .FirstOrDefault();
        var whereClause = $"1 = 1";
        DataWarehouseAPIModel apiCallModel;
        var fileNameList = new List<FileSubmissionOutputDTO>();
        var client = new RestClient();
        RestRequest restRequest;
        var camundaVariables = new
        {
            value = "submitted",
            type = "string"
        };

        whereClause += $" AND FileName = '{adhocFileSubmission.FileName}' AND FileStatus != 'FILE_VALIDATION_FAILED'";
        apiCallModel = new DataWarehouseAPIModel()
        {
            Columns = "FileName, TransactionDate, DWDateCreated, FileStatus",
            Where = whereClause
        };

        try
        {
            fileNameList = _dataWarehouseService
                .GetResultArray<FileSubmissionOutputDTO>("API.vwDailyAndMonthlyFileSubmissions", apiCallModel).ToList();
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }

        if (fileNameList.Count > 0)
        {
            camundaVariables = new
            {
                value = "submitted",
                type = "string"
            };

            //adhocFileSubmission.SubmissionLastCheckedDate = DateTime.Now; // Does not exist on Ad-Hoc table
            adhocFileSubmission.ActualSubmissionDate = fileNameList.FirstOrDefault().TransactionDate;
            //adhocFileSubmission.DWDateCreated = fileNameList.FirstOrDefault().DWDateCreated; // Does not exist on Ad-Hoc table
        }
        else
        {
            camundaVariables = new
            {
                value = "notSubmitted",
                type = "string"
            };

            //adhocFileSubmission.SubmissionLastCheckedDate = DateTime.Now; // Does not exist on Ad-Hoc table
        }

        try
        {
            restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/process-instance/" +
                                          task.ProcessInstanceId + "/variables/fileSubmittedOnPlannedDate")
                .AddJsonBody(JsonConvert.SerializeObject(camundaVariables));
            client.Put(restRequest);
            _dbContext.SaveChanges();
        }
        catch (Exception exception)
        {
            _globalHelper.LogError(_dbContext, exception,
                $"Unable to complete task to check if adhoc file was submitted to DTH ({task.Id})");
            throw new Exception($"Unable to complete task to check if adhoc file was submitted to DTH ({task.Id})");
        }
    }

    public void UpdateAdHocFileSubmissionStatusToApproved(string processInstanceId)
    {
        var taskVariables = _globalHelper.GetVariables(processInstanceId);
        int adHocFileSubmissionRequestId;
        AdhocFileSubmission adHocFileSubmission;

        if (taskVariables != null)
        {
            adHocFileSubmissionRequestId =
                int.Parse(taskVariables.Find(x => x.Name == "FileSubmissionRequestId").Value);
        }
        else
        {
            throw new Exception($"Unable to fetch variables for task with process instance ID ({processInstanceId})");
        }

        if (adHocFileSubmissionRequestId > 0)
        {
            adHocFileSubmission = _dbContext.AdhocFileSubmissions
                .Where(x => x.Id == adHocFileSubmissionRequestId)
                .FirstOrDefault();
        }
        else
        {
            throw new Exception($"Unable to find replacement file submission with ID ({adHocFileSubmissionRequestId})");
        }

        try
        {
            adHocFileSubmission.AdhocFileSubmissionStatusId = (int)ReplacementFileSubmissionStatuses.Approved;
            adHocFileSubmission.SubmissionStatusDate = DateTime.Now;
            _dbContext.AdhocFileSubmissions.Update(adHocFileSubmission);
            _dbContext.SaveChanges();
        }
        catch
        {
            throw new Exception($"Unable to update adhoc file submission with ID ({adHocFileSubmissionRequestId})");
        }
    }
}