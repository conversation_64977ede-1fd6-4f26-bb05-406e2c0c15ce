using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using AutoMapper;
using Camunda.Api.Client;
using Camunda.Api.Client.ProcessDefinition;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Business.Resources.SRN;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Migrations;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Business.Services;

public class SrnStatusUpdateCamundaService
{
    private readonly AppDbContext _dbContext;
    private readonly EmailService _emailService;
    private readonly IMapper _mapper;
    private readonly SRNStatusRepository _statusRepository;
    private CamundaClient _camundaClient;
    private readonly ConfigSettings _configSettings;

    public SrnStatusUpdateCamundaService(AppDbContext dbContext, EmailService emailService, IMapper mapper,
        SRNStatusRepository statusRepository, IOptions<ConfigSettings> configSettings)
    {
        _dbContext = dbContext;
        _emailService = emailService;
        _mapper = mapper;
        _statusRepository = statusRepository;
        _configSettings = configSettings.Value;
        var httpClient = new HttpClient
        {
            BaseAddress = new Uri(_configSettings.CamundaBaseAddress)
        };
        _camundaClient = CamundaClient.Create(httpClient);
    }

    private async Task<List<EventLog>> GetEventLogs(int entityId, int entityTypeId, string changeType = null)
    {
        var eventLogs = await _dbContext.EventLogs
            .Where(i => i.EntityId == entityId && i.EntityTypeId == entityTypeId)
            .ToListAsync();

        if (!string.IsNullOrEmpty(changeType) && eventLogs != null)
        {
            eventLogs = eventLogs.Where(i => i.ChangeType == changeType).ToList();
        }

        return eventLogs;
    }

    private async Task<StagingChange> GetOldSRNStatusFromEventLog(int srnId, int index = 0)
    {
        var entityType = await _dbContext.EntityTypes.FirstOrDefaultAsync(i => i.Name == "SRN");
        if (entityType != null)
        {
            var eventLogs = await GetEventLogs(srnId, entityType.Id, "SRN Update");
            var changesBlob = new List<MemberStagingChangeLogResource>();

            if (eventLogs != null)
            {
                eventLogs = eventLogs.OrderByDescending(i => i.Id).ToList();

                foreach (var log in eventLogs)
                {
                    var stagingChange = JsonConvert.DeserializeObject<MemberStagingChangeLogResource>(log.ChangeBlob);
                    changesBlob.Add(stagingChange);
                }
            }

            var changes = new List<StagingChange>();
            foreach (var change in changesBlob)
            {
                changes.AddRange(change.Changes.Where(i => i.Name == "SRN Status").ToList());
            }

            var changeLog = new StagingChange();
            if (changes != null)
            {
                if (changes.Count > 0)
                {
                    if (index < changes.Count)
                        return changeLog = changes[index];
                }
            }
        }

        return null;
    }

    public async Task EmailMemberAboutSRNPendingClosure(int srnId)
    {
        try
        {
            var selectRecord = await _dbContext.SRNs
                .Include(i => i.Member)
                .ThenInclude(x => x.Contacts)
                .Include(i => i.SPGroup)
                .Include(i => i.SRNStatus)
                .Include(i => i.SRNStatusReason)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == srnId);

            string message = selectRecord.SRNNumber;

            if (selectRecord != null)
            {
                List<KeyValuePair<string, string>> placeholders = new List<KeyValuePair<string, string>>();

                var srnNumber = (!string.IsNullOrEmpty(selectRecord.SRNNumber))
                    ? selectRecord.SRNNumber
                    : "Not Available";
                placeholders.Add(new KeyValuePair<string, string>("[SRNNumber]", srnNumber));
                placeholders.Add(new KeyValuePair<string, string>("[MemberRegisteredName]",
                    selectRecord.Member.RegisteredName));

                var tradingName = (selectRecord.TradingName != null) ? selectRecord.TradingName : "Not Specified";
                placeholders.Add(new KeyValuePair<string, string>("[SRNDisplayName]", tradingName));

                var oldSRNStatus = "Not Available";
                var changeLog = await GetOldSRNStatusFromEventLog(selectRecord.Id, 0);
                if (changeLog != null)
                {
                    oldSRNStatus = (!string.IsNullOrEmpty(changeLog.OldValue)) ? changeLog.OldValue : "Not Available";
                }

                placeholders.Add(new KeyValuePair<string, string>("[OldSRNStatus]", oldSRNStatus));

                var newSRNStatus = (selectRecord.SRNStatus != null) ? selectRecord.SRNStatus.Name : "Not Available";
                placeholders.Add(new KeyValuePair<string, string>("[NewSRNStatus]", newSRNStatus));

                var reasonForUpdate = (selectRecord.SRNStatusReason != null)
                    ? selectRecord.SRNStatusReason.Name
                    : "Not Available";
                placeholders.Add(new KeyValuePair<string, string>("[ReasonForUpdate]", reasonForUpdate));

                var contactTypes = await _dbContext.ContactTypes
                    .AsNoTracking()
                    .Where(i => i.Name == "Main Contact Details"
                                || i.Name == "Alternate Contact Details"
                                || i.Name == "Financial Contact Details")
                    .ToListAsync();

                if (contactTypes != null)
                {
                    ICollection<MemberContact> contacts = new List<MemberContact>();

                    if (selectRecord.Member.MembershipTypeId == MembershipTypes.ALGClient)
                    {
                        contacts = await _dbContext.MemberContacts
                            .Where(i => i.MemberId == selectRecord.ALGLeaderId)
                            .ToListAsync();
                    }
                    else
                    {
                        contacts = selectRecord.Member.Contacts;
                    }

                    foreach (var type in contactTypes)
                    {
                        var contact = contacts
                            .FirstOrDefault(i => i.ContactTypeId == type.Id);

                        if (contact != null)
                        {
                            if (!string.IsNullOrEmpty(contact.Email))
                                _emailService.SendEmail(contact.Email, contact.FirstName, "SRN Status Update",
                                    "EmailMemberAboutSRNPendingClosure.html", placeholders, null, "", "",
                                    selectRecord.Id, WorkflowEnum.SRNStatusUpdate, EmailReasonEnum.SRNUpdated,
                                    EmailRecipientTypeEnum.Member);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email member for SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public async Task EmailBureausAboutSRNPendingClosure(int srnId)
    {
        try
        {
            var selectRecord = await _dbContext.SRNs
                .Include(i => i.Member)
                .Include(i => i.SRNStatus)
                .Include(i => i.SRNStatusReason)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == srnId);

            string message = selectRecord.SRNNumber;

            if (selectRecord != null)
            {
                List<KeyValuePair<string, string>> placeholders = new List<KeyValuePair<string, string>>();

                var srnNumber = (!string.IsNullOrEmpty(selectRecord.SRNNumber))
                    ? selectRecord.SRNNumber
                    : "Not Available";
                placeholders.Add(new KeyValuePair<string, string>("[SRNNumber]", srnNumber));
                placeholders.Add(new KeyValuePair<string, string>("[MemberRegisteredName]",
                    selectRecord.Member.RegisteredName));

                var tradingName = (selectRecord.TradingName != null) ? selectRecord.TradingName : "Not Specified";
                placeholders.Add(new KeyValuePair<string, string>("[SRNDisplayName]", tradingName));

                var oldSRNStatus = "Not Available";
                var changeLog = await GetOldSRNStatusFromEventLog(selectRecord.Id, 0);
                if (changeLog != null)
                {
                    oldSRNStatus = (!string.IsNullOrEmpty(changeLog.OldValue)) ? changeLog.OldValue : "Not Available";
                }

                placeholders.Add(new KeyValuePair<string, string>("[OldSRNStatus]", oldSRNStatus));

                var newSRNStatus = (selectRecord.SRNStatus != null) ? selectRecord.SRNStatus.Name : "Not Available";
                placeholders.Add(new KeyValuePair<string, string>("[NewSRNStatus]", newSRNStatus));

                var reasonForUpdate = (selectRecord.SRNStatusReason != null)
                    ? selectRecord.SRNStatusReason.Name
                    : "Not Available";
                placeholders.Add(new KeyValuePair<string, string>("[ReasonForUpdate]", reasonForUpdate));

                var lastSubmissionDate = (selectRecord.LastSubmissionDate != null)
                    ? selectRecord.LastSubmissionDate.Value.ToString("yyyy-MM-dd")
                    : "N/A";
                placeholders.Add(new KeyValuePair<string, string>("[LastSubmissionDate]", lastSubmissionDate));

                var bureauInstruction = (!string.IsNullOrEmpty(selectRecord.BureauInstruction))
                    ? selectRecord.BureauInstruction
                    : "N/A";
                placeholders.Add(new KeyValuePair<string, string>("[BureauInstruction]", bureauInstruction));

                var statusDate = (selectRecord.AccountStatusDate != null)
                    ? selectRecord.AccountStatusDate.Value.ToString("yyyy-MM-dd")
                    : "N/A";
                placeholders.Add(new KeyValuePair<string, string>("[StatusDate]", statusDate));

                var comments = (!string.IsNullOrEmpty(selectRecord.Comments)) ? selectRecord.Comments : "N/A";
                placeholders.Add(new KeyValuePair<string, string>("[StatusComment]", comments));

                var bureaus = await _dbContext.Members
                    .Include(i => i.Contacts)
                    .Where(i => i.MembershipTypeId == MembershipTypes.Bureau)
                    .ToListAsync();

                var contactTypes = await _dbContext.ContactTypes
                    .AsNoTracking()
                    .Where(i => i.Name == "Data Contact Details")
                    .ToListAsync();

                foreach (var bureau in bureaus)
                {
                    if (contactTypes != null)
                    {
                        foreach (var type in contactTypes)
                        {
                            var contact = bureau.Contacts
                                .FirstOrDefault(i => i.ContactTypeId == type.Id);

                            if (contact != null)
                            {
                                if (!string.IsNullOrEmpty(contact.Email))
                                    _emailService.SendEmail(contact.Email, contact.FirstName, "SRN Status Update",
                                        "EmailBureausAboutSRNPendingClosure.html", placeholders, null, "", "",
                                        selectRecord.Id, WorkflowEnum.SRNStatusUpdate, EmailReasonEnum.SRNUpdated,
                                        EmailRecipientTypeEnum.Bureau);
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email bureau for SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public async Task UpdateSRNStatus(int srnId, string newStatus, int updatedByUserId = 0,
        string processInstanceId = null, bool isNewSRN = false)
    {
        var srn = await _dbContext.Set<SRN>()
            .Include(i => i.SRNStatus)
            .Include(i => i.SRNStatusUpdates)
            .FirstOrDefaultAsync(i => i.Id == srnId);

        var newSRNStatus = await _statusRepository.GetByName(newStatus);
        var srnStatus = srn.SRNStatus;

        //This is a hack to get the current/old status
        //for some reason sometimes the SRNStatus does not get INCLUDED in the SRN object
        if (srnStatus == null && srn.SRNStatusId > 0)
        {
            srnStatus = await _dbContext.SRNStatuses
                .FirstOrDefaultAsync(i => i.Id == srn.SRNStatusId);
        }

        var oldStatus = (srnStatus != null) ? srnStatus.Name : "";

        var stagingChange = new StagingChange
        {
            Name = "SRN Status",
            OldValue = oldStatus,
            NewValue = newStatus
        };

        var rolloutStatus = await _dbContext.RolloutStatuses
            .FirstOrDefaultAsync(i => i.Name == newStatus);

        if (srn != null && srn.SRNStatusUpdates.Count > 0 && !string.IsNullOrEmpty(processInstanceId))
        {
            var recentStatusUpdate = srn.SRNStatusUpdates.FirstOrDefault(i => i.ProcessInstanceId == processInstanceId);
            if (recentStatusUpdate != null)
            {
                if (recentStatusUpdate.IsLiveFileSubmissionsSuspended || isNewSRN)
                {
                    //If there are 2 files(daily and monthly)
                    if (isNewSRN && srn.FileType == SRNStatusFileTypes.MonthlyAndDailyFile &&
                        srn.SRNStatusId != recentStatusUpdate.SRNStatusId)
                    {
                        recentStatusUpdate.SRNStatusId = newSRNStatus.Id;
                        recentStatusUpdate.RolloutStatusId = (rolloutStatus != null)
                            ? rolloutStatus.Id
                            : recentStatusUpdate.RolloutStatusId;

                        await _dbContext.SaveChangesAsync();
                    }
                    else if (isNewSRN && (srn.FileType == SRNStatusFileTypes.DailyFile ||
                                          srn.FileType == SRNStatusFileTypes.MonthlyFile)
                             || recentStatusUpdate.IsLiveFileSubmissionsSuspended
                             || (srn.FileType == SRNStatusFileTypes.MonthlyAndDailyFile &&
                                 srn.SRNStatusId == recentStatusUpdate.SRNStatusId))
                    {
                        //This is a hack to get the current/old status
                        //for some reason sometimes the SRNStatus does not get INCLUDED in the SRN object
                        if (srnStatus == null && srn.SRNStatusId > 0)
                        {
                            srnStatus = await _dbContext.SRNStatuses
                                .FirstOrDefaultAsync(i => i.Id == srn.SRNStatusId);
                        }

                        srn.SRNStatusId = (newSRNStatus != null) ? newSRNStatus.Id : srn.SRNStatusId;
                        srn.StatusLastUpdatedAt = DateTime.Now;

                        _dbContext.Attach(srn);
                        _dbContext.Entry(srn).Property("SRNStatusId").IsModified = true;
                        _dbContext.Entry(srn).Property("StatusLastUpdatedAt").IsModified = true;

                        if (recentStatusUpdate != null)
                        {
                            recentStatusUpdate.SRNStatusId = srn.SRNStatusId;
                            recentStatusUpdate.RolloutStatusId = (rolloutStatus != null)
                                ? rolloutStatus.Id
                                : recentStatusUpdate.RolloutStatusId;
                        }

                        await _dbContext.SaveChangesAsync();
                    }
                    else
                    {
                        recentStatusUpdate.SRNStatusId = newSRNStatus.Id;
                        recentStatusUpdate.RolloutStatusId = (rolloutStatus != null)
                            ? rolloutStatus.Id
                            : recentStatusUpdate.RolloutStatusId;

                        await _dbContext.SaveChangesAsync();
                    }
                }
                else if (recentStatusUpdate.IsLiveFileSubmissionsSuspended && !isNewSRN)
                {
                    srn.SRNStatusId = (newSRNStatus != null) ? newSRNStatus.Id : srn.SRNStatusId;
                    srn.StatusLastUpdatedAt = DateTime.Now;

                    _dbContext.Attach(srn);
                    _dbContext.Entry(srn).Property("SRNStatusId").IsModified = true;
                    _dbContext.Entry(srn).Property("StatusLastUpdatedAt").IsModified = true;

                    if (recentStatusUpdate != null)
                    {
                        recentStatusUpdate.SRNStatusId = srn.SRNStatusId;
                        recentStatusUpdate.RolloutStatusId = (rolloutStatus != null)
                            ? rolloutStatus.Id
                            : recentStatusUpdate.RolloutStatusId;
                    }

                    await _dbContext.SaveChangesAsync();
                }
                else if (!recentStatusUpdate.IsLiveFileSubmissionsSuspended)
                {
                    recentStatusUpdate.SRNStatusId = newSRNStatus.Id;

                    //This will need to be changed in future.
                    //We need to check the file type that was selected before changing the 
                    //main SRN status.
                    if (newSRNStatus.Name == "Live" || newSRNStatus.Name == "Running Down" ||
                        newSRNStatus.Name == "Dormant")
                    {
                        srn.SRNStatusId = (newSRNStatus != null) ? newSRNStatus.Id : srn.SRNStatusId;
                        srn.StatusLastUpdatedAt = DateTime.Now;

                        _dbContext.Attach(srn);
                        _dbContext.Entry(srn).Property("SRNStatusId").IsModified = true;
                        _dbContext.Entry(srn).Property("StatusLastUpdatedAt").IsModified = true;


                        if (recentStatusUpdate != null && rolloutStatus != null)
                        {
                            recentStatusUpdate.SRNStatusId = srn.SRNStatusId;
                            recentStatusUpdate.RolloutStatusId = (rolloutStatus != null)
                                ? rolloutStatus.Id
                                : recentStatusUpdate.RolloutStatusId;
                        }
                    }

                    await _dbContext.SaveChangesAsync();
                }

                if (srn.SRNStatusId != 2)
                {
                    var newSrnStatusEntry = new SrnStatusHistory()
                    {
                        SrnId = srn.Id,
                        StatusId = srn.SRNStatusId,
                        StatusDate = DateTime.Now,
                        StatusReasonId = srn.SRNStatusReasonId
                    };

                    _dbContext.SrnStatusHistory.Add(newSrnStatusEntry);
                }

                _dbContext.SaveChanges();

                var srnUpdateResource = _mapper.Map<SRNUpdateResource>(srn);
                var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);

                var stagingChangeLog = new MemberStagingChangeLogResource();

                stagingChangeLog.Changes.Add(stagingChange);

                var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);

                await Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", srn.TradingName,
                    entityBlob, changeBlob, srn.Id, "SRN");
            }

            else if (recentStatusUpdate == null
                     && (newStatus == "Dormant" || newStatus == "Live" || newStatus == "Running Down"
                         || newStatus == "Closure Pending" || newStatus == "Closed"))
            {
                srn.SRNStatusId = (newSRNStatus != null) ? newSRNStatus.Id : srn.SRNStatusId;
                srn.StatusLastUpdatedAt = DateTime.Now;

                _dbContext.Attach(srn);
                _dbContext.Entry(srn).Property("SRNStatusId").IsModified = true;
                _dbContext.Entry(srn).Property("StatusLastUpdatedAt").IsModified = true;

                if (srn.SRNStatusId != 2)
                {
                    var newSrnStatusEntry = new SrnStatusHistory()
                    {
                        SrnId = srn.Id,
                        StatusId = srn.SRNStatusId,
                        StatusDate = DateTime.Now,
                        StatusReasonId = srn.SRNStatusReasonId
                    };

                    _dbContext.SrnStatusHistory.Add(newSrnStatusEntry);
                }

                _dbContext.SaveChanges();
                
                var srnUpdateResource = _mapper.Map<SRNUpdateResource>(srn);
                var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);

                var stagingChangeLog = new MemberStagingChangeLogResource();

                stagingChangeLog.Changes.Add(stagingChange);

                var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);

                await Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", srn.TradingName,
                    entityBlob, changeBlob, srn.Id, "SRN");
            }
        }

        else if (srn != null || (srn != null && isNewSRN))
        {
            //This is a hack to get the current/old status
            //for some reason sometimes the SRNStatus does not get INCLUDED in the SRN object
            if (srnStatus == null && srn.SRNStatusId > 0)
            {
                srnStatus = await _dbContext.SRNStatuses
                    .FirstOrDefaultAsync(i => i.Id == srn.SRNStatusId);
            }

            srn.SRNStatusId = (newSRNStatus != null) ? newSRNStatus.Id : srn.SRNStatusId;
            srn.StatusLastUpdatedAt = DateTime.Now;

            _dbContext.Attach(srn);
            _dbContext.Entry(srn).Property("SRNStatusId").IsModified = true;
            _dbContext.Entry(srn).Property("StatusLastUpdatedAt").IsModified = true;

            var recentStatusUpdate = srn.SRNStatusUpdates.FirstOrDefault(i => i.ProcessInstanceId == processInstanceId);
            if (recentStatusUpdate != null)
            {
                recentStatusUpdate.SRNStatusId = srn.SRNStatusId;
                recentStatusUpdate.RolloutStatusId =
                    (rolloutStatus != null) ? rolloutStatus.Id : recentStatusUpdate.RolloutStatusId;
            }

            if (srn.SRNStatusId != 2)
            {
                var newSrnStatusEntry = new SrnStatusHistory()
                {
                    SrnId = srn.Id,
                    StatusId = srn.SRNStatusId,
                    StatusDate = DateTime.Now,
                    StatusReasonId = srn.SRNStatusReasonId
                };

                _dbContext.SrnStatusHistory.Add(newSrnStatusEntry);
            }

            await _dbContext.SaveChangesAsync();

            var srnUpdateResource = _mapper.Map<SRNUpdateResource>(srn);
            var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);

            var stagingChangeLog = new MemberStagingChangeLogResource();

            stagingChangeLog.Changes.Add(stagingChange);

            var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);

            await Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", srn.TradingName, entityBlob,
                changeBlob, srn.Id, "SRN");
        }
    }

    public async Task EmailBureausToClosePaymentProfile(int srnId)
    {
        try
        {
            var selectRecord = await _dbContext.SRNs
                .Include(i => i.Member)
                .Include(i => i.SRNStatus)
                .Include(i => i.SRNStatusReason)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == srnId);

            string message = selectRecord.SRNNumber;

            if (selectRecord != null)
            {
                List<KeyValuePair<string, string>> placeholders = new List<KeyValuePair<string, string>>();

                var srnNumber = (!string.IsNullOrEmpty(selectRecord.SRNNumber))
                    ? selectRecord.SRNNumber
                    : "Not Available";
                placeholders.Add(new KeyValuePair<string, string>("[SRNNumber]", srnNumber));
                placeholders.Add(new KeyValuePair<string, string>("[MemberRegisteredName]",
                    selectRecord.Member.RegisteredName));

                var tradingName = (selectRecord.TradingName != null) ? selectRecord.TradingName : "Not Specified";
                placeholders.Add(new KeyValuePair<string, string>("[SRNDisplayName]", tradingName));

                var oldSRNStatus = "Not Available";
                var changeLog = await GetOldSRNStatusFromEventLog(selectRecord.Id, 0);
                if (changeLog != null)
                {
                    oldSRNStatus = (!string.IsNullOrEmpty(changeLog.OldValue)) ? changeLog.OldValue : "Not Available";
                }

                placeholders.Add(new KeyValuePair<string, string>("[OldSRNStatus]", oldSRNStatus));

                var newSRNStatus = (selectRecord.SRNStatus != null) ? selectRecord.SRNStatus.Name : "Not Available";
                placeholders.Add(new KeyValuePair<string, string>("[NewSRNStatus]", newSRNStatus));

                var reasonForUpdate = (selectRecord.SRNStatusReason != null)
                    ? selectRecord.SRNStatusReason.Name
                    : "Not Available";
                placeholders.Add(new KeyValuePair<string, string>("[ReasonForUpdate]", reasonForUpdate));

                var lastSubmissionDate = (selectRecord.LastSubmissionDate != null)
                    ? selectRecord.LastSubmissionDate.Value.ToString("yyyy-MM-dd")
                    : "N/A";
                placeholders.Add(new KeyValuePair<string, string>("[LastSubmissionDate]", lastSubmissionDate));

                var bureauInstruction = (!string.IsNullOrEmpty(selectRecord.BureauInstruction))
                    ? selectRecord.BureauInstruction
                    : "N/A";
                placeholders.Add(new KeyValuePair<string, string>("[BureauInstruction]", bureauInstruction));

                var statusDate = (selectRecord.AccountStatusDate != null)
                    ? selectRecord.AccountStatusDate.Value.ToString("yyyy-MM-dd")
                    : "N/A";
                placeholders.Add(new KeyValuePair<string, string>("[StatusDate]", statusDate));

                var comments = (!string.IsNullOrEmpty(selectRecord.Comments)) ? selectRecord.Comments : "N/A";
                placeholders.Add(new KeyValuePair<string, string>("[StatusComment]", comments));

                var bureaus = await _dbContext.Members
                    .Include(i => i.Contacts)
                    .Where(i => i.MembershipTypeId == MembershipTypes.Bureau)
                    .ToListAsync();

                var contactTypes = await _dbContext.ContactTypes
                    .AsNoTracking()
                    .Where(i => i.Name == "Data Contact Details")
                    .ToListAsync();

                foreach (var bureau in bureaus)
                {
                    if (contactTypes != null)
                    {
                        foreach (var type in contactTypes)
                        {
                            var contact = bureau.Contacts
                                .FirstOrDefault(i => i.ContactTypeId == type.Id);

                            if (contact != null)
                            {
                                if (!string.IsNullOrEmpty(contact.Email))
                                    _emailService.SendEmail(contact.Email, contact.FirstName, "SRN Status Update",
                                        "EmailBureausToClosePaymentProfile.html", placeholders, null, "", "",
                                        selectRecord.Id, WorkflowEnum.SRNStatusUpdate, EmailReasonEnum.SRNUpdated,
                                        EmailRecipientTypeEnum.Bureau);
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email bureau for SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public async Task EmailMemberToConfirmClosure(int srnId)
    {
        try
        {
            var selectRecord = await _dbContext.SRNs
                .Include(i => i.Member)
                .ThenInclude(x => x.Contacts)
                .Include(i => i.SPGroup)
                .Include(i => i.SRNStatus)
                .Include(i => i.SRNStatusReason)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == srnId);

            string message = selectRecord.SRNNumber;

            if (selectRecord != null)
            {
                List<KeyValuePair<string, string>> placeholders = new List<KeyValuePair<string, string>>();

                var srnNumber = (!string.IsNullOrEmpty(selectRecord.SRNNumber))
                    ? selectRecord.SRNNumber
                    : "Not Available";
                placeholders.Add(new KeyValuePair<string, string>("[SRNNumber]", srnNumber));
                placeholders.Add(new KeyValuePair<string, string>("[MemberRegisteredName]",
                    selectRecord.Member.RegisteredName));

                var tradingName = (selectRecord.TradingName != null) ? selectRecord.TradingName : "Not Specified";
                placeholders.Add(new KeyValuePair<string, string>("[SRNDisplayName]", tradingName));

                var oldSRNStatus = "Not Available";
                var changeLog = await GetOldSRNStatusFromEventLog(selectRecord.Id, 1);
                if (changeLog != null)
                {
                    oldSRNStatus = (!string.IsNullOrEmpty(changeLog.OldValue)) ? changeLog.OldValue : "Not Available";
                }

                placeholders.Add(new KeyValuePair<string, string>("[OldSRNStatus]", oldSRNStatus));

                var newSRNStatus = (selectRecord.SRNStatus != null) ? selectRecord.SRNStatus.Name : "Not Available";
                placeholders.Add(new KeyValuePair<string, string>("[NewSRNStatus]", newSRNStatus));

                var reasonForUpdate = (selectRecord.SRNStatusReason != null)
                    ? selectRecord.SRNStatusReason.Name
                    : "Not Available";
                placeholders.Add(new KeyValuePair<string, string>("[ReasonForUpdate]", reasonForUpdate));

                if (selectRecord.Member.MembershipTypeId == MembershipTypes.ALGClient)
                {
                    if (selectRecord.ALGLeaderId > 0)
                    {
                        var dataContact =
                            await Helpers.Helpers.GetMemberDataContact(_dbContext, (int)selectRecord.ALGLeaderId);

                        if (dataContact != null)
                        {
                            _emailService.SendEmail(dataContact.Email, dataContact.FirstName, "SRN Status Update",
                                "EmailMemberToConfirmClosure.html", placeholders, null, "", "",
                                selectRecord.Id, WorkflowEnum.SRNStatusUpdate, EmailReasonEnum.SRNUpdated,
                                EmailRecipientTypeEnum.Member);
                        }
                    }
                }
                else
                {
                    var contactTypes = await _dbContext.ContactTypes
                        .AsNoTracking()
                        .Where(i => i.Name == "Main Contact Details"
                                    || i.Name == "Alternate Contact Details"
                                    || i.Name == "Financial Contact Details")
                        .ToListAsync();

                    if (contactTypes != null)
                    {
                        foreach (var type in contactTypes)
                        {
                            var contact = selectRecord.Member.Contacts
                                .FirstOrDefault(i => i.ContactTypeId == type.Id);

                            if (contact != null)
                            {
                                if (!string.IsNullOrEmpty(contact.Email))
                                {
                                    _emailService.SendEmail(contact.Email, contact.FirstName, "SRN Status Update",
                                        "EmailMemberToConfirmClosure.html", placeholders, null, "", "",
                                        selectRecord.Id, WorkflowEnum.SRNStatusUpdate, EmailReasonEnum.SRNUpdated,
                                        EmailRecipientTypeEnum.Member);
                                }
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email member for SRN Id " + srnId + ". " + ex.Message;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public async Task KickoffMemberAutoCloseWorkflowIfMemberHasNoActiveSRNs(int srnId)
    {
        string[] nonActiveStatuses = new string[]
        {
            "Rejected", "Sold", "Closed", "Dormant",
            "Deactivated", "Deactivated - Split", "Deactivated - Merged"
        };

        var srn = _dbContext.SRNs
            .Include(i => i.Member)
            .Where(i => i.Id == srnId)
            .AsQueryable()
            .FirstOrDefault();

        if (srn != null)
        {
            var memberActiveSrns = _dbContext.SRNs
                .Include(i => i.SRNStatus)
                .Where(i => i.MemberId == srn.MemberId && !nonActiveStatuses.Contains(i.SRNStatus.Name))
                .AsQueryable();

            //If member has no active SRNs
            if (memberActiveSrns.Count() <= 0)
            {
                // Kick of the member auto close workflow
                await _camundaClient.ProcessDefinitions.ByKey("Member-Auto-Close-on-SRN-Closure").StartProcessInstance(
                    new StartProcessInstance()
                    {
                        Variables = new Dictionary<string, VariableValue>()
                        {
                            { "memberId", VariableValue.FromObject(srn.MemberId) },
                            {
                                "stakeHolderManagerAssignee",
                                VariableValue.FromObject(srn.Member.StakeholderManagerId.ToString())
                            }
                        }
                    });
            }
        }
    }
    
    public async Task SetSRNStatusDateToCurrentDate(int srnId)
    {
        var srn = await _dbContext.Set<SRN>()
            .FirstOrDefaultAsync(i => i.Id == srnId);

        srn.StatusLastUpdatedAt = DateTime.Now;
        await _dbContext.SaveChangesAsync();
    }
}