using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using AutoMapper;
using Camunda.Api.Client.ExternalTask;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using RestSharp;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Business.Resources.SRN;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Business.Services;

public class SrnStatusUpdateToTestCamundaService
{
    private readonly SRNRepository _srnRepository;
    private readonly AppDbContext _dbContext;
    private readonly EmailService _emailService;
    private readonly ConfigSettings _configSettings;
    public IMapper _mapper { get; }

    public SrnStatusUpdateToTestCamundaService(SRNRepository srnRepository, AppDbContext dbContext,
        EmailService emailService, IOptions<ConfigSettings> configSettings, IMapper mapper)
    {
        _srnRepository = srnRepository;
        _dbContext = dbContext;
        _emailService = emailService;
        _configSettings = configSettings.Value;
        _mapper = mapper;
    }

    public void UpdateSRNStatus(int srnId, string newStatus, int updatedByUserId = 0,
        string processInstanceId = null, bool isNewSRN = false)
    {
        _srnRepository.UpdateSRNStatus(srnId, newStatus, updatedByUserId, processInstanceId, isNewSRN);
    }

    public void UpdateRolloutStatus(string statusName, string processInstanceId)
    {
        var rolloutStatus = _dbContext.RolloutStatuses
            .FirstOrDefaultAsync(i => i.Name == statusName);

        if (rolloutStatus != null && !string.IsNullOrEmpty(processInstanceId))
        {
            var srnStatusUpdateHistory = _dbContext.Set<SRNStatusUpdateHistory>()
                .Include(i => i.SRN)
                .FirstOrDefaultAsync(i => i.ProcessInstanceId == processInstanceId);

            if (srnStatusUpdateHistory != null)
            {
                if (statusName == "Live")
                {
                    srnStatusUpdateHistory.IsComple = true;
                    srnStatusUpdateHistory.DateCompleted = DateTime.Now;
                    srnStatusUpdateHistory.SignoffDate = DateTime.Now;

                    srnStatusUpdateHistory.SRN.SignoffDate = DateTime.Now;

                    if (srnStatusUpdateHistory.FileType == SRNStatusFileTypes.DailyFile)
                    {
                        srnStatusUpdateHistory.IsDailyFileLive = true;
                    }
                    else if (srnStatusUpdateHistory.FileType == SRNStatusFileTypes.DailyFile)
                    {
                        srnStatusUpdateHistory.IsMonthlyFileLive = true;
                    }
                }

                srnStatusUpdateHistory.RolloutStatusId = rolloutStatus.Id;
                _dbContext.SaveChanges();
            }
        }
    }

    private static SRNStatusUpdateHistory GetRecentSRNStatusUpdateHistory(
        ICollection<SRNStatusUpdateHistory> srnStatusUpdates, string fileType)
    {
        if (srnStatusUpdates != null)
        {
            if (srnStatusUpdates.Count > 0)
            {
                if (fileType != null)
                {
                    if (fileType == "MonthlyFile")
                        srnStatusUpdates = srnStatusUpdates.Where(i => i.FileType == SRNStatusFileTypes.MonthlyFile)
                            .ToList();
                    else if (fileType == "DailyFile")
                        srnStatusUpdates = srnStatusUpdates.Where(i => i.FileType == SRNStatusFileTypes.DailyFile)
                            .ToList();
                }

                if (srnStatusUpdates != null)
                {
                    if (srnStatusUpdates.Count > 0)
                    {
                        srnStatusUpdates = srnStatusUpdates.OrderByDescending(i => i.DateCreated).ToList();
                        return srnStatusUpdates.FirstOrDefault();
                    }
                }
            }
        }

        return new SRNStatusUpdateHistory();
    }

    public void EmailBureausAfterSRNTesting(int srnId, string emailSubject = "New SRN Application")
    {
        try
        {
            var selectRecord = _dbContext.SRNs
                .Include(i => i.Member)
                .Include(i => i.SPGroup)
                .Include(i => i.SRNStatusUpdates)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == srnId);

            string message = selectRecord.SRNNumber;

            if (selectRecord != null)
            {
                List<KeyValuePair<string, string>> placeholders = new List<KeyValuePair<string, string>>();

                placeholders.Add(new KeyValuePair<string, string>("[MemberRegisteredName]",
                    selectRecord.Member.RegisteredName));
                placeholders.Add(new KeyValuePair<string, string>("[SRNNumber]", selectRecord.SRNNumber));

                var tradingName = (selectRecord.TradingName != null) ? selectRecord.TradingName : "Not Specified";
                placeholders.Add(new KeyValuePair<string, string>("[SRNDisplayName]", tradingName));

                var dailySRNStatusUpdate =
                    GetRecentSRNStatusUpdateHistory(selectRecord.SRNStatusUpdates, "DailyFile");
                var monthlySRNStatusUpdate =
                    GetRecentSRNStatusUpdateHistory(selectRecord.SRNStatusUpdates, "MonthlyFile");

                var dailyGoLiveDate = (dailySRNStatusUpdate.DailyFileGoLiveDate != null)
                    ? string.Format("{0:yyyy-MM-dd}", dailySRNStatusUpdate.DailyFileGoLiveDate.Value)
                    : "Not Specified";
                var monthlyGoLiveDate = (monthlySRNStatusUpdate.MonthlyFileGoLiveDate != null)
                    ? string.Format("{0:yyyy-MM-dd}", monthlySRNStatusUpdate.MonthlyFileGoLiveDate.Value)
                    : "Not Specified";

                placeholders.Add(new KeyValuePair<string, string>("[DailyGoLiveDate]", dailyGoLiveDate));
                placeholders.Add(new KeyValuePair<string, string>("[MonthlyGoLiveDate]", monthlyGoLiveDate));

                var bureaus = _dbContext.Members
                    .Include(i => i.Contacts)
                    .AsNoTracking()
                    .Where(i => i.MembershipTypeId == MembershipTypes.Bureau && i.RegisteredName != "TRANSUNION")
                    .ToListAsync();

                var mainContactType = _dbContext.ContactTypes
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Name == "Data Contact Details");

                foreach (var entity in bureaus)
                {
                    if (mainContactType != null)
                    {
                        var mainContact = entity.Contacts
                            .FirstOrDefault(i => i.ContactTypeId == mainContactType.Id);

                        if (mainContact != null)
                        {
                            if (!string.IsNullOrEmpty(mainContact.Email))
                                _emailService.SendEmail(mainContact.Email, mainContact.FirstName, emailSubject,
                                    "EmailBureausAfterSRNTesting.html", placeholders);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email bureaus for SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public void EmailBureausOnSRNGoLive(int srnId)
    {
        try
        {
            var selectRecord = _dbContext.SRNs
                .Include(i => i.Member)
                .Include(i => i.SPGroup)
                .Include(i => i.SRNStatusUpdates)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == srnId);

            string message = selectRecord.SRNNumber;

            if (selectRecord != null)
            {
                List<KeyValuePair<string, string>> placeholders = new List<KeyValuePair<string, string>>();

                placeholders.Add(new KeyValuePair<string, string>("[MemberRegisteredName]",
                    selectRecord.Member.RegisteredName));

                var tradingName = (selectRecord.TradingName != null) ? selectRecord.TradingName : "Not Specified";
                placeholders.Add(new KeyValuePair<string, string>("[SRNNumber]", selectRecord.SRNNumber));

                var dailySRNStatusUpdate =
                    GetRecentSRNStatusUpdateHistory(selectRecord.SRNStatusUpdates, "DailyFile");
                var monthlySRNStatusUpdate =
                    GetRecentSRNStatusUpdateHistory(selectRecord.SRNStatusUpdates, "MonthlyFile");

                var dailyGoLiveDate = (dailySRNStatusUpdate.DailyFileGoLiveDate != null)
                    ? string.Format("{0:yyyy-MM-dd}", dailySRNStatusUpdate.DailyFileGoLiveDate.Value)
                    : "Not Specified";
                var monthlyGoLiveDate = (monthlySRNStatusUpdate.MonthlyFileGoLiveDate != null)
                    ? string.Format("{0:yyyy-MM-dd}", monthlySRNStatusUpdate.MonthlyFileGoLiveDate.Value)
                    : "Not Specified";

                placeholders.Add(new KeyValuePair<string, string>("[DailyGoLiveDate]", dailyGoLiveDate));
                placeholders.Add(new KeyValuePair<string, string>("[MonthlyGoLiveDate]", monthlyGoLiveDate));

                var bureaus = _dbContext.Members
                    .Include(i => i.Contacts)
                    .AsNoTracking()
                    .Where(i => i.MembershipTypeId == MembershipTypes.Bureau && i.RegisteredName != "TRANSUNION")
                    .ToListAsync();

                var mainContactType = _dbContext.ContactTypes
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Name == "Data Contact Details");

                foreach (var entity in bureaus)
                {
                    if (mainContactType != null)
                    {
                        var mainContact = entity.Contacts
                            .FirstOrDefault(i => i.ContactTypeId == mainContactType.Id);

                        if (mainContact != null)
                        {
                            if (!string.IsNullOrEmpty(mainContact.Email))
                                _emailService.SendEmail(mainContact.Email, mainContact.FirstName, "SRN is Live",
                                    "EmailBureausSRNIsLive.html", placeholders);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email bureaus for SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }
    
    public async Task<List<VariableInstanceGetResource>> GetVariables(string processInstanceId)
    {
        try
        {
            using (var client = new HttpClient())
            {
                var uri = _configSettings.CamundaBaseAddress + "/variable-instance?processInstanceIdIn=" + processInstanceId;
                var result = client.Get(uri);
                result.EnsureSuccessStatusCode();

                var resultString = result.Content.ReadAsStringAsync();
                var variablesResourceList = JsonConvert.DeserializeObject<List<VariableInstanceGetResource>>(resultString);

                return variablesResourceList;
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to retrieve variables for process id " + processInstanceId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }
    
    public void UpdateSrnAndFileStatuses(int srnId, string processInstanceId, int updatedByUserId, ExternalTaskResource task)
    {
        var srnTask = task.Get();
        var processVariables = GetVariables(srnTask.ProcessInstanceId);
        var isNewSrn = bool.Parse(processVariables.Find(x => x.Name == "newSrn").Value);
        var selectedSrn = _dbContext.SRNs
            .Include(x => x.SRNStatusReason)
            .FirstOrDefault(x => x.Id == srnId);
        var srnStatus = _dbContext.SRNStatuses
            .FirstOrDefault(x => x.Id == selectedSrn.SRNStatusId);
        
        if (!isNewSrn)
        {
            var selectedSrnFileList = _dbContext.vwSRNWithUpdateHistories
                .Where(x => x.SRNId == srnId && x.IsLatestHistory == 1)
                .ToList();

            if (srnStatus == null)
            {
                throw new Exception("SRN Status not found");
            }

            if (selectedSrn == null)
            {
                throw new Exception("Selected SRN does not exist.");
            }

            // Scenario 1A
            // SRN Live, Daily File Live, No Monthly File
            // If Daily File is place back in Test and IsSubmissionSuspended = 0
            // Then SRN stays Live and Daily File is placed in Test 
            // When Daily File is confirmed Live again, SRN status is unaffected, Daily file is made Live
            if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Count == 1)
            {
                if (!bool.Parse(processVariables.Find(x => x.Name == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes)int.Parse(processVariables.Find(x => x.Name == "fileType").Value) == SRNStatusFileTypes.DailyFile)
                {
                        var newSrnFileEntry = new SRNStatusUpdateHistory
                        {
                            DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileDevelopmentStartDate").Value),
                            DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileDevelopmentEndDate").Value),
                            DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileTestStartDate").Value),
                            DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileTestEndDate").Value),
                            DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileGoLiveDate").Value),
                            DateCreated = DateTime.Now,
                            FileType = SRNStatusFileTypes.DailyFile,
                            IsComple = false,
                            IsLiveFileSubmissionsSuspended = false,
                            SRNId = selectedSrn.Id,
                            SRNStatusId = 17, // Test
                            ProcessInstanceId = processInstanceId,
                            RolloutStatusId = 4, // Test
                            SignoffDate = null,
                            SRNFileTestingStatusReason = processVariables.Find(x => x.Name == "srnFileTestingStatusReason").Value
                        };
                        
                        _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                        _dbContext.SRNs.Update(selectedSrn);
                        
                        var stagingChange = new StagingChange
                        {
                            Name = "SRN File Status History Create/Update",
                            OldValue = "Empty",
                            NewValue = "Empty"
                        };
                        var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
                        var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                        var stagingChangeLog = new MemberStagingChangeLogResource();
                        stagingChangeLog.Changes.Add(stagingChange);
                        var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                        Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 1B
            // SRN Live, Daily File Live, No Monthly File
            // If Daily File is place back in Test and IsSubmissionSuspended = 1
            // Then SRN is placed in Test and Daily File is placed in Test
            // When Daily File is confirmed Live again, SRN status is made Live, Daily file is made Live
            if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Count == 1)
            {
                if (bool.Parse(processVariables.Find(x => x.Name == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes)int.Parse(processVariables.Find(x => x.Name == "fileType").Value) == SRNStatusFileTypes.DailyFile)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory
                    {
                        DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileDevelopmentStartDate").Value),
                        DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileDevelopmentEndDate").Value),
                        DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileTestStartDate").Value),
                        DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileTestEndDate").Value),
                        DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.DailyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name == "srnFileTestingStatusReason").Value
                    };
                    selectedSrn.SRNStatusId = 17; // Test
                    selectedSrn.StatusLastUpdatedAt = DateTime.Now;
                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                    _dbContext.SRNs.Update(selectedSrn);
                    
                    var stagingChange = new StagingChange
                    {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                    
                    var newSrnStatusEntry = new SrnStatusHistory()
                    {
                        SrnId = selectedSrn.Id,
                        StatusId = selectedSrn.SRNStatusId,
                        StatusDate = DateTime.Now,
                        StatusReasonId = selectedSrn.SRNStatusReasonId
                    };
                        
                    _dbContext.SrnStatusHistory.Add(newSrnStatusEntry);
                    _dbContext.SaveChanges();
                }
            }

            // Scenario 2A
            // SRN Live, No Daily File, Monthly File Live
            // If Monthly File is place back in Test and IsSubmissionSuspended = 0
            // Then SRN stays Live and Monthly File is placed in Test
            // When Monthly File is confirmed Live again, SRN status is unaffected, Monthly file is made Live
            if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Count == 1)
            {
                if (!bool.Parse(processVariables.Find(x => x.Name == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes)int.Parse(processVariables.Find(x => x.Name == "fileType").Value) == SRNStatusFileTypes.MonthlyFile)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory
                    {
                        MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileDevelopmentStartDate").Value),
                        MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileDevelopmentEndDate").Value),
                        MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileTestStartDate").Value),
                        MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileTestEndDate").Value),
                        MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.MonthlyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = false,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                    _dbContext.SRNs.Update(selectedSrn);
                    
                    var stagingChange = new StagingChange
                    {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 2B
            // SRN Live, No Daily File, Monthly File Live
            // If Monthly File is place back in Test and IsSubmissionSuspended = 1
            // Then SRN is placed in Test and Monthly File is placed in Test
            // When Monthly File is confirmed Live again, SRN status is made Live, Monthly file is made Live
            if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Count == 1)
            {
                if (bool.Parse(processVariables.Find(x => x.Name == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes)int.Parse(processVariables.Find(x => x.Name == "fileType").Value) == SRNStatusFileTypes.MonthlyFile)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory
                    {
                        MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileDevelopmentStartDate").Value),
                        MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileDevelopmentEndDate").Value),
                        MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileTestStartDate").Value),
                        MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileTestEndDate").Value),
                        MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.MonthlyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name == "srnFileTestingStatusReason").Value
                    };
                    
                    selectedSrn.SRNStatusId = 17; // Test
                    selectedSrn.StatusLastUpdatedAt = DateTime.Now;
                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                    _dbContext.SRNs.Update(selectedSrn);
                    
                    var stagingChange = new StagingChange
                    {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                    
                    var newSrnStatusEntry = new SrnStatusHistory()
                    {
                        SrnId = selectedSrn.Id,
                        StatusId = selectedSrn.SRNStatusId,
                        StatusDate = DateTime.Now,
                        StatusReasonId = selectedSrn.SRNStatusReasonId
                    };
                        
                    _dbContext.SrnStatusHistory.Add(newSrnStatusEntry);
                    _dbContext.SaveChanges();
                }
            }

            // Scenario 3A
            // SRN Live, Daily File Live, Monthly File Live
            // If Daily File is place back in Test and IsSubmissionSuspended = 0
            // Then SRN stays Live and Daily File is placed in Test
            // When Daily File is confirmed Live again, SRN status is unaffected, Daily file is made Live
            if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Count == 2)
            {
                if (!bool.Parse(processVariables.Find(x => x.Name == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes)int.Parse(processVariables.Find(x => x.Name == "fileType").Value) == SRNStatusFileTypes.DailyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.MonthlyFile).HistoryStatusId == 4)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory
                    {
                        DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileDevelopmentStartDate").Value),
                        DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileDevelopmentEndDate").Value),
                        DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileTestStartDate").Value),
                        DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileTestEndDate").Value),
                        DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.DailyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = false,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                    _dbContext.SRNs.Update(selectedSrn);
                    
                    var stagingChange = new StagingChange
                    {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 3B
            // SRN Live, Daily File Live, Monthly File Live
            // If Daily File is place back in Test and IsSubmissionSuspended = 1
            // Then SRN stays Live and Daily File is placed in Test
            // When Daily File is confirmed Live again, SRN status is unaffected, Daily file is made Live
            if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Count == 2)
            {
                if (bool.Parse(processVariables.Find(x => x.Name == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes)int.Parse(processVariables.Find(x => x.Name == "fileType").Value) == SRNStatusFileTypes.DailyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.MonthlyFile).HistoryStatusId == 4)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory
                    {
                        DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileDevelopmentStartDate").Value),
                        DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileDevelopmentEndDate").Value),
                        DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileTestStartDate").Value),
                        DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileTestEndDate").Value),
                        DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.DailyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                    _dbContext.SRNs.Update(selectedSrn);
                    
                    var stagingChange = new StagingChange
                    {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 4A
            // SRN Live, Daily File Live, Monthly File Live
            // If Monthly File is place back in Test and IsSubmissionSuspended = 0
            // Then SRN stays Live and Monthly File is placed in Test
            // When Monthly File is confirmed Live again, SRN status is unaffected, Monthly file is made Live
            if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Count == 2)
            {
                if (!bool.Parse(processVariables.Find(x => x.Name == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes)int.Parse(processVariables.Find(x => x.Name == "fileType").Value) == SRNStatusFileTypes.MonthlyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.DailyFile).HistoryStatusId == 4)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory
                    {
                        MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileDevelopmentStartDate").Value),
                        MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileDevelopmentEndDate").Value),
                        MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileTestStartDate").Value),
                        MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileTestEndDate").Value),
                        MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.MonthlyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = false,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                    _dbContext.SRNs.Update(selectedSrn);
                    
                    var stagingChange = new StagingChange
                    {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 4B
            // SRN Live, Daily File Live, Monthly File Live
            // If Monthly File is place back in Test and IsSubmissionSuspended = 1
            // Then SRN stays Live and Monthly File is placed in Test
            // When Monthly File is confirmed Live again, SRN status is unaffected, Monthly file is made Live
            if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Count == 2)
            {
                if (bool.Parse(processVariables.Find(x => x.Name == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes)int.Parse(processVariables.Find(x => x.Name == "fileType").Value) == SRNStatusFileTypes.MonthlyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.DailyFile).HistoryStatusId == 4)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory
                    {
                        MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileDevelopmentStartDate").Value),
                        MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileDevelopmentEndDate").Value),
                        MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileTestStartDate").Value),
                        MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileTestEndDate").Value),
                        MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.MonthlyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                    _dbContext.SRNs.Update(selectedSrn);
                    
                    var stagingChange = new StagingChange
                    {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 5A
            // SRN Live, Daily File Live, Monthly File Test with IsSubmissionSuspended = 0 (workflow exists for testing)
            // If Daily File is place back in Test and IsSubmissionSuspended = 0
            // Then SRN stays Live and Daily File is placed in Test
            // When Daily File is confirmed Live again, SRN status is unaffected, Daily file is made Live
            if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Count == 2)
            {
                if (!bool.Parse(processVariables.Find(x => x.Name == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes)int.Parse(processVariables.Find(x => x.Name == "fileType").Value) == SRNStatusFileTypes.DailyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.MonthlyFile).HistoryStatusId == 17
                    && !selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.MonthlyFile).IsLiveFileSubmissionsSuspended)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory
                    {
                        DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileDevelopmentStartDate").Value),
                        DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileDevelopmentEndDate").Value),
                        DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileTestStartDate").Value),
                        DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileTestEndDate").Value),
                        DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.DailyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = false,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                    _dbContext.SRNs.Update(selectedSrn);
                    
                    var stagingChange = new StagingChange
                    {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 5B
            // SRN Live, Daily File Live, Monthly File Test with IsSubmissionSuspended = 1 (workflow exists for testing)
            // If Daily File is place back in Test and IsSubmissionSuspended = 0
            // Then SRN stays Live and Daily File is placed in Test
            // When Daily File is confirmed Live again, SRN status is unaffected, Daily file is made Live
            if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Count == 2)
            {
                if (!bool.Parse(processVariables.Find(x => x.Name == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes)int.Parse(processVariables.Find(x => x.Name == "fileType").Value) == SRNStatusFileTypes.DailyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.MonthlyFile).HistoryStatusId == 17
                    && selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.MonthlyFile).IsLiveFileSubmissionsSuspended)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory
                    {
                        DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileDevelopmentStartDate").Value),
                        DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileDevelopmentEndDate").Value),
                        DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileTestStartDate").Value),
                        DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileTestEndDate").Value),
                        DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.DailyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = false,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                    _dbContext.SRNs.Update(selectedSrn);
                    
                    var stagingChange = new StagingChange
                    {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 5C
            // SRN Live, Daily File Live, Monthly File Test with IsSubmissionSuspended = 0 (workflow exists for testing)
            // If Daily File is place back in Test and IsSubmissionSuspended = 1
            // Then SRN stays Live and Daily File is placed in Test
            // When Daily File is confirmed Live again, SRN status is unaffected, Daily file is made Live
            if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Count == 2)
            {
                if (bool.Parse(processVariables.Find(x => x.Name == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes)int.Parse(processVariables.Find(x => x.Name == "fileType").Value) == SRNStatusFileTypes.DailyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.MonthlyFile).HistoryStatusId == 17
                    && !selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.MonthlyFile).IsLiveFileSubmissionsSuspended)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory
                    {
                        DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileDevelopmentStartDate").Value),
                        DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileDevelopmentEndDate").Value),
                        DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileTestStartDate").Value),
                        DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileTestEndDate").Value),
                        DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.DailyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                    _dbContext.SRNs.Update(selectedSrn);
                    
                    var stagingChange = new StagingChange
                    {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 5D
            // SRN Live, Daily File Live, Monthly File Test with IsSubmissionSuspended = 1 (workflow exists for testing)
            // If Daily File is place back in Test and IsSubmissionSuspended = 1
            // Then SRN is place back in Test and Daily File is placed in Test
            // When Daily File is confirmed Live again, SRN status is made Live, Daily file is made Live
            if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Count == 2)
            {
                if (bool.Parse(processVariables.Find(x => x.Name == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes)int.Parse(processVariables.Find(x => x.Name == "fileType").Value) == SRNStatusFileTypes.DailyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.MonthlyFile).HistoryStatusId == 17
                    && selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.MonthlyFile).IsLiveFileSubmissionsSuspended)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory
                    {
                        DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileDevelopmentStartDate").Value),
                        DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileDevelopmentEndDate").Value),
                        DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileTestStartDate").Value),
                        DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileTestEndDate").Value),
                        DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "dailyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.DailyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name == "srnFileTestingStatusReason").Value
                    };
                    
                    selectedSrn.SRNStatusId = 17; // Test
                    selectedSrn.StatusLastUpdatedAt = DateTime.Now;
                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                    _dbContext.SRNs.Update(selectedSrn);
                    
                    var stagingChange = new StagingChange
                    {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                    
                    var newSrnStatusEntry = new SrnStatusHistory()
                    {
                        SrnId = selectedSrn.Id,
                        StatusId = selectedSrn.SRNStatusId,
                        StatusDate = DateTime.Now,
                        StatusReasonId = selectedSrn.SRNStatusReasonId
                    };
                        
                    _dbContext.SrnStatusHistory.Add(newSrnStatusEntry);
                    _dbContext.SaveChanges();
                }
            }

            // Scenario 6A (Future Plan)
            // SRN Live, Monthly File Live, Daily File Test with IsSubmissionSuspended = 0 (workflow exists for testing)
            // If Monthly File is place back in Test and IsSubmissionSuspended = 0
            // Then SRN stays Live and Monthly File is placed in Test
            // When Monthly File is confirmed Live again, SRN status is unaffected, Monthly file is made Live
            if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Count == 2)
            {
                if (!bool.Parse(processVariables.Find(x => x.Name == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes)int.Parse(processVariables.Find(x => x.Name == "fileType").Value) == SRNStatusFileTypes.MonthlyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.DailyFile).HistoryStatusId == 17
                    && !selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.DailyFile).IsLiveFileSubmissionsSuspended)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory
                    {
                        MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileDevelopmentStartDate").Value),
                        MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileDevelopmentEndDate").Value),
                        MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileTestStartDate").Value),
                        MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileTestEndDate").Value),
                        MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.MonthlyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = false,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                    _dbContext.SRNs.Update(selectedSrn);
                    
                    var stagingChange = new StagingChange
                    {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 6B (Future Plan)
            // SRN Live, Monthly File Live, Daily File Test with IsSubmissionSuspended = 1 (workflow exists for testing)
            // If Monthly File is place back in Test and IsSubmissionSuspended = 0
            // Then SRN stays Live and Monthly File is placed in Test
            // When Monthly File is confirmed Live again, SRN status is unaffected, Monthly file is made Live
            if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Count == 2)
            {
                if (!bool.Parse(processVariables.Find(x => x.Name == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes)int.Parse(processVariables.Find(x => x.Name == "fileType").Value) == SRNStatusFileTypes.MonthlyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.DailyFile).HistoryStatusId == 17
                    && selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.DailyFile).IsLiveFileSubmissionsSuspended)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory
                    {
                        MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileDevelopmentStartDate").Value),
                        MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileDevelopmentEndDate").Value),
                        MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileTestStartDate").Value),
                        MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileTestEndDate").Value),
                        MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.MonthlyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = false,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                    _dbContext.SRNs.Update(selectedSrn);
                    
                    var stagingChange = new StagingChange
                    {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 6C (Future Plan)
            // SRN Live, Monthly File Live, Daily File Test with IsSubmissionSuspended = 0 (workflow exists for testing)
            // If Monthly File is place back in Test and IsSubmissionSuspended = 1
            // Then SRN stays Live and Monthly File is placed in Test
            // When Monthly File is confirmed Live again, SRN status is unaffected, Monthly file is made Live
            if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Count == 2)
            {
                if (bool.Parse(processVariables.Find(x => x.Name == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes)int.Parse(processVariables.Find(x => x.Name == "fileType").Value) == SRNStatusFileTypes.MonthlyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.DailyFile).HistoryStatusId == 17
                    && !selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.DailyFile).IsLiveFileSubmissionsSuspended)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory
                    {
                        MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileDevelopmentStartDate").Value),
                        MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileDevelopmentEndDate").Value),
                        MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileTestStartDate").Value),
                        MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileTestEndDate").Value),
                        MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.MonthlyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name == "srnFileTestingStatusReason").Value
                    };

                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                    _dbContext.SRNs.Update(selectedSrn);
                    
                    var stagingChange = new StagingChange
                    {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                }
            }

            // Scenario 6D (Future Plan)
            // SRN Live, Monthly File Live, Daily File Test with IsSubmissionSuspended = 1 (workflow exists for testing)
            // If Monthly File is place back in Test and IsSubmissionSuspended = 1
            // Then SRN is place back in Test and Monthly File is placed in Test
            // When Monthly File is confirmed Live again, SRN status is made Live, Monthly file is made Live
            if (selectedSrn.SRNStatusId == 4 && selectedSrnFileList.Count == 2)
            {
                if (bool.Parse(processVariables.Find(x => x.Name == "isLiveFileSubmissionSuspended").Value)
                    && (SRNStatusFileTypes)int.Parse(processVariables.Find(x => x.Name == "fileType").Value) == SRNStatusFileTypes.MonthlyFile
                    && selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.DailyFile).HistoryStatusId == 17
                    && selectedSrnFileList.Find(x => x.HistoryFileType == (int)SRNStatusFileTypes.DailyFile).IsLiveFileSubmissionsSuspended)
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory
                    {
                        MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileDevelopmentStartDate").Value),
                        MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileDevelopmentEndDate").Value),
                        MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileTestStartDate").Value),
                        MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileTestEndDate").Value),
                        MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "monthlyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.MonthlyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null,
                        SRNFileTestingStatusReason = processVariables.Find(x => x.Name == "srnFileTestingStatusReason").Value
                    };
                    
                    selectedSrn.SRNStatusId = 17; // Test
                    selectedSrn.StatusLastUpdatedAt = DateTime.Now;
                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                    _dbContext.SRNs.Update(selectedSrn);
                    
                    var stagingChange = new StagingChange
                    {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                    
                    var newSrnStatusEntry = new SrnStatusHistory()
                    {
                        SrnId = selectedSrn.Id,
                        StatusId = selectedSrn.SRNStatusId,
                        StatusDate = DateTime.Now,
                        StatusReasonId = selectedSrn.SRNStatusReasonId
                    };
                        
                    _dbContext.SrnStatusHistory.Add(newSrnStatusEntry);
                    _dbContext.SaveChanges();
                }
            }
        }
        else
        { 
            var fileType = (SRNStatusFileTypes)int.Parse(processVariables.Find(x => x.Name == "fileType").Value);
            switch (fileType)
            {
                case SRNStatusFileTypes.DailyFile:
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory
                    {
                        DailyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "DailyFileDevelopmentStartDate").Value),
                        DailyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "DailyFileDevelopmentEndDate").Value),
                        DailyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "DailyFileTestStartDate").Value),
                        DailyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "DailyFileTestEndDate").Value),
                        DailyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "DailyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.DailyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null
                    };
                    
                    selectedSrn.SRNStatusId = 17; // Test
                    selectedSrn.StatusLastUpdatedAt = DateTime.Now;
                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                    _dbContext.SRNs.Update(selectedSrn);
                    
                    var stagingChange = new StagingChange
                    {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                    break;
                }

                case SRNStatusFileTypes.MonthlyFile:
                {
                    var newSrnFileEntry = new SRNStatusUpdateHistory
                    {
                        MonthlyFileDevelopmentStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "MonthlyFileDevelopmentStartDate").Value),
                        MonthlyFileDevelopmentEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "MonthlyFileDevelopmentEndDate").Value),
                        MonthlyFileTestStartDate = DateTime.Parse(processVariables.Find(x => x.Name == "MonthlyFileTestStartDate").Value),
                        MonthlyFileTestEndDate = DateTime.Parse(processVariables.Find(x => x.Name == "MonthlyFileTestEndDate").Value),
                        MonthlyFileGoLiveDate = DateTime.Parse(processVariables.Find(x => x.Name == "MonthlyFileGoLiveDate").Value),
                        DateCreated = DateTime.Now,
                        FileType = SRNStatusFileTypes.MonthlyFile,
                        IsComple = false,
                        IsLiveFileSubmissionsSuspended = true,
                        SRNId = selectedSrn.Id,
                        SRNStatusId = 17, // Test
                        BureauInstruction = selectedSrn.BureauInstruction,
                        Comments = selectedSrn.Comments,
                        LastSubmissionDate = selectedSrn.LastSubmissionDate,
                        ProcessInstanceId = processInstanceId,
                        RolloutStatusId = 4, // Test
                        SignoffDate = null
                    };
                    
                    selectedSrn.SRNStatusId = 17; // Test
                    selectedSrn.StatusLastUpdatedAt = DateTime.Now;
                    _dbContext.SRNStatusUpdateHistory.Add(newSrnFileEntry);
                    _dbContext.SRNs.Update(selectedSrn);
                    
                    var stagingChange = new StagingChange
                    {
                        Name = "SRN File Status History Create/Update",
                        OldValue = "Empty",
                        NewValue = "Empty"
                    };
                    var srnUpdateResource = _mapper.Map<SRNUpdateResource>(selectedSrn);
                    var entityBlob = JsonConvert.SerializeObject(srnUpdateResource);
                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    stagingChangeLog.Changes.Add(stagingChange);
                    var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                    Helpers.Helpers.CreateEventLog(_dbContext, updatedByUserId, "SRN Update", selectedSrn.TradingName, entityBlob, changeBlob, selectedSrn.Id, "SRN");
                    break;
                }
            }   
        }
    }
}