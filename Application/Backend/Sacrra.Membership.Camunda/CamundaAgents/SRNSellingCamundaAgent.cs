using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Services;

namespace Sacrra.Membership.Camunda.CamundaAgents;

public class SrnSellingCamundaAgent : BaseCamundaAgent
{
    public SrnSellingCamundaAgent(CamundaClient camundaClient) : base(camundaClient)
    {
    }

    public override async Task Process(string topicName, ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        switch (topicName)
        {
            case "re-allocate-srn-from-seller-to-buyer":
                await ReAllocateSrnFromSellerToBuyer(task, completeExternalTask, serviceScope);
                break;

            case "notify-member-of-SRN-sale-request-cancellation":
                await NotifyMemberOfSrnSaleRequestCancellation(task, completeExternalTask, serviceScope);
                break;

            case "notify-seller-about-unregistered-buyer":
                await NotifySellerAboutUnregisteredBuyer(task, completeExternalTask, serviceScope);
                break;

            case "notify-buyer-member-to-start-new-srn-registration-process":
                await NotifyBuyerMemberToStartNewSrnRegistrationProcess(task, completeExternalTask, serviceScope);
                break;

            case "notify-bureus-and-shm-of-sale-file-submission-completion":
                await NotifyBureusAndShmOfSaleFileSubmissionCompletion(task, completeExternalTask, serviceScope);
                break;

            case "notify-member-about-srn-split-or-merge-cancellation":
                await NotifyMemberAboutSrnSplitOrMKergeCancellation(task, completeExternalTask, serviceScope);
                break;

            case "notify-seller-and-shm-of-sale-request-commencement":
                await NotifySellerAndShmOfSaleRequestCommencement(task, completeExternalTask, serviceScope);
                break;

            case "split-merge-sale-update-srn-status-after-testing":
                await SplitMergeSaleUpdateSrnStatusAfterTesting(task, completeExternalTask, serviceScope);
                break;

            case "split-merge-sale-email-bureaus-on-srn-go-live":
                await SplitMergeSaleEmailBureausOnSrnGoLive(task, completeExternalTask, serviceScope);
                break;
        }

        await task.Complete(completeExternalTask);
    }

    private async Task SplitMergeSaleEmailBureausOnSrnGoLive(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var requestType = await GetGenericVariable(task, "RequestType", serviceScope);

        if (requestType.ToLower() == "sale")
        {
            var srnId = await GetMemberVariable(task, "BuyerSRNId", serviceScope);

            if (srnId > 0)
            {
                await GetSrnSellingCamundaService(serviceScope).EmailBureausOnSRNGoLive(srnId);
            }
        }
        else if (requestType.ToLower() == "split")
        {
            var splitListVariable = await GetGenericVariable(task, "SRNIdSplitList", serviceScope);
            if (!string.IsNullOrEmpty(splitListVariable))
            {
                var splitList = splitListVariable.Split(',');

                foreach (var srnId in splitList)
                {
                    await GetSrnSellingCamundaService(serviceScope).EmailBureausOnSRNGoLive(Convert.ToInt32(srnId));
                }
            }
        }
        else if (requestType.ToLower() == "merge")
        {
            var mergeToSRNId = await GetGenericVariable(task, "MergeToSRNId", serviceScope);
            if (!string.IsNullOrEmpty(mergeToSRNId))
            {
                await GetSrnSellingCamundaService(serviceScope).EmailBureausOnSRNGoLive(Convert.ToInt32(mergeToSRNId));
            }
        }

        var randomUser =
            await GetRandomUser("SACRRAAdministrator"); //TODO: Waiting for Leon to confirm how do we allocate
        // the SACRRA Admin. For now, I just select any random user that
        // has the SACRRA Administrator role.

        completeExternalTask.Variables = new Dictionary<string, VariableValue>
        {
            ["SACRRAAdminAssignee"] = VariableValue.FromObject(randomUser.Id)
        };
    }

    private static async Task SplitMergeSaleUpdateSrnStatusAfterTesting(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = 0;

        var requestType = await GetGenericVariable(task, "RequestType", serviceScope);

        if (requestType.ToLower() == "sale")
        {
            srnId = await GetMemberVariable(task, "BuyerSRNId", serviceScope);
            var taskInfo = await task.Get();

            var processInstanceId = string.Empty;
            if (taskInfo != null)
            {
                processInstanceId = taskInfo.ProcessInstanceId;
            }

            if (srnId > 0)
            {
                await GetSrnSellingCamundaService(serviceScope).UpdateSRNStatus(srnId, "Test", 0, processInstanceId);
            }
        }
        else if (requestType.ToLower() == "split")
        {
            var splitListVariable = await GetGenericVariable(task, "SRNIdSplitList", serviceScope);
            if (!string.IsNullOrEmpty(splitListVariable))
            {
                var splitList = splitListVariable.Split(',');
                var taskInfo = await task.Get();

                var processInstanceId = string.Empty;
                if (taskInfo != null)
                {
                    processInstanceId = taskInfo.ProcessInstanceId;
                }

                foreach (var splitToId in splitList)
                {
                    await GetSrnSellingCamundaService(serviceScope)
                        .UpdateSRNStatus(Convert.ToInt32(splitToId), "Test", 0, processInstanceId);
                }
            }
        }
        else if (requestType.ToLower() == "merge")
        {
            var mergeToSRNId = await GetGenericVariable(task, "MergeToSRNId", serviceScope);
            var taskInfo = await task.Get();

            var processInstanceId = string.Empty;
            if (taskInfo != null)
            {
                processInstanceId = taskInfo.ProcessInstanceId;
            }

            if (!string.IsNullOrEmpty(mergeToSRNId))
            {
                await GetSrnSellingCamundaService(serviceScope)
                    .UpdateSRNStatus(Convert.ToInt32(mergeToSRNId), "Test", 0, processInstanceId);
            }
        }
    }

    private static async Task NotifySellerAndShmOfSaleRequestCommencement(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetIntegerVariable(task, "SRNIdToBeSold", serviceScope);

        await GetSrnSellingCamundaService(serviceScope).NotifySellerAndSHMOfSaleRequestCommencement(srnId);
    }

    private static async Task NotifyMemberAboutSrnSplitOrMKergeCancellation(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var requestType = await GetGenericVariable(task, "RequestType", serviceScope);

        if (requestType.ToLower() == "split")
        {
            var splitFromSRNId = await GetGenericVariable(task, "SplitFromSRNId", serviceScope);
            if (!string.IsNullOrEmpty(splitFromSRNId))
            {
                var splitType = await GetGenericVariable(task, "SaleType", serviceScope);
                if (splitType.ToLower() == "full")
                {
                    var initialSRNStatusId = await GetGenericVariable(task, "InitialStatusId", serviceScope);

                    if (!string.IsNullOrEmpty(initialSRNStatusId))
                    {
                        await GetSrnSellingCamundaService(serviceScope).UpdateSRNStatus(Convert.ToInt32(splitFromSRNId),
                            Convert.ToInt32(initialSRNStatusId));
                    }
                }

                await GetSrnSellingCamundaService(serviceScope)
                    .NotifyMemberOfSRNSplitCancellation(Convert.ToInt32(splitFromSRNId));
            }
        }
        else if (requestType.ToLower() == "merge")
        {
            var mergeToSRNId = await GetGenericVariable(task, "MergeToSRNId", serviceScope);

            if (!string.IsNullOrEmpty(mergeToSRNId))
            {
                var mergeType = await GetGenericVariable(task, "SaleType", serviceScope);
                if (mergeType.ToLower() == "full")
                {
                    var initialSRNStatusIds = await GetGenericVariable(task, "MergeListInitialStatusIds", serviceScope);
                    if (!string.IsNullOrEmpty(initialSRNStatusIds))
                    {
                        var srnsToBeUpdated = initialSRNStatusIds.Split(',');
                        foreach (var srn in srnsToBeUpdated)
                        {
                            var idStatusPair = srn.Split(':');
                            if (idStatusPair.Length > 1)
                            {
                                var srnId = Convert.ToInt32(idStatusPair[0]);
                                var statusId = Convert.ToInt32(idStatusPair[1]);

                                if (srnId > 0 && statusId > 0)
                                {
                                    await GetSrnSellingCamundaService(serviceScope).UpdateSRNStatus(srnId, statusId);
                                }
                            }
                        }
                    }
                }

                await GetSrnSellingCamundaService(serviceScope)
                    .NotifyMemberOfSRNMergeCancellation(Convert.ToInt32(mergeToSRNId));
            }
        }
        else if (requestType.ToLower() == "sale")
        {
            var srnToBeSoldId = await GetGenericVariable(task, "SRNIdToBeSold", serviceScope);
            if (!string.IsNullOrEmpty(srnToBeSoldId))
            {
                var reviewComments = await GetGenericVariable(task, "ReviewCommentsBuyer", serviceScope);

                if (!string.IsNullOrEmpty(srnToBeSoldId))
                    await GetSrnSellingCamundaService(serviceScope)
                        .NotifyMemberOfSRNSaleRequestCancellation(Convert.ToInt32(srnToBeSoldId), reviewComments);
            }
        }
    }

    private static async Task NotifyBureusAndShmOfSaleFileSubmissionCompletion(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = 0;

        var requestType = await GetGenericVariable(task, "RequestType", serviceScope);

        if (requestType == "split")
            srnId = await GetIntegerVariable(task, "SplitFromSRNId", serviceScope);
        else if (requestType == "merge")
            srnId = await GetIntegerVariable(task, "MergeToSRNId", serviceScope);
        else if (requestType == "sale")
            srnId = await GetIntegerVariable(task, "SRNIdToBeSold", serviceScope);

        await GetSrnSellingCamundaService(serviceScope).NotifyBureusAndSHMOfSaleFileSubmissionCompletion(srnId);
    }

    private static async Task NotifyBuyerMemberToStartNewSrnRegistrationProcess(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var requestType = await GetGenericVariable(task, "RequestType", serviceScope);

        if (requestType.ToLower() == "sale")
        {
            var srnId = await GetMemberVariable(task, "SRNIdToBeSold", serviceScope);
            var buyerMemberId = await GetMemberVariable(task, "BuyerMemberId", serviceScope);

            if (srnId > 0)
            {
                await GetSrnSellingCamundaService(serviceScope)
                    .NotifyBuyerMemberToStartNewSRNRegistrationProcess(srnId, buyerMemberId);
            }
        }
        else if (requestType.ToLower() == "split")
        {
            var splitFromSRNId = await GetGenericVariable(task, "SplitFromSRNId", serviceScope);
            if (!string.IsNullOrEmpty(splitFromSRNId))
            {
                await GetSrnSellingCamundaService(serviceScope)
                    .NotifyBuyerMemberToStartNewSRNRegistrationProcess_SplitOrMerge(Convert.ToInt32(splitFromSRNId),
                        requestType.ToLower());
            }
        }
        else if (requestType.ToLower() == "merge")
        {
            var mergeToSRNId = await GetGenericVariable(task, "MergeToSRNId", serviceScope);
            if (!string.IsNullOrEmpty(mergeToSRNId))
            {
                await GetSrnSellingCamundaService(serviceScope)
                    .NotifyBuyerMemberToStartNewSRNRegistrationProcess_SplitOrMerge(Convert.ToInt32(mergeToSRNId),
                        requestType.ToLower());
            }
        }
    }

    private static async Task NotifySellerAboutUnregisteredBuyer(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var requestType = await GetGenericVariable(task, "RequestType", serviceScope);

        if (requestType != null)
        {
            if (requestType == "sale")
            {
                var srnId = await GetIntegerVariable(task, "SRNIdToBeSold", serviceScope);
                await GetSrnSellingCamundaService(serviceScope).NotifySellerAboutUnregisteredBuyer(srnId);
            }
        }
    }

    private static async Task NotifyMemberOfSrnSaleRequestCancellation(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetIntegerVariable(task, "SRNIdToBeSold", serviceScope);
        var reviewComments = await GetGenericVariable(task, "ReviewCommentsSeller", serviceScope);

        await GetSrnSellingCamundaService(serviceScope).NotifyMemberOfSRNSaleRequestCancellation(srnId, reviewComments);
    }

    private static async Task ReAllocateSrnFromSellerToBuyer(ExternalTaskResource task,
        CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var taskObject = await task.Get();
        var processInstanceId = taskObject.ProcessInstanceId;

        await GetSrnSellingCamundaService(serviceScope).ReAllocateSRNFromSellerToBuyer(processInstanceId);
    }

    private static SrnSellingCamundaService GetSrnSellingCamundaService(IServiceScope serviceScope)
    {
        return serviceScope.ServiceProvider.GetRequiredService<SrnSellingCamundaService>();
    }
}