using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Services;

namespace Sacrra.Membership.Camunda.CamundaAgents
{
    public class MemberStatusUpdateCamundaAgent : BaseCamundaAgent
    {
        public MemberStatusUpdateCamundaAgent(CamundaClient camundaClient) : base(camundaClient) { }

        public override async Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            switch (topicName)
            {
                case "email-member-to-state-removal":
                    await EmailMemberToStateRemoval(task, completeExternalTask, serviceScope);
                    break;

                case "enable-or-disable-member-users":
                    await EnableOrDisableMemberUsers(task, completeExternalTask, serviceScope);
                    break;
            }
            
            await task.Complete(completeExternalTask);
        }

        private async Task EnableOrDisableMemberUsers(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var memberId = await GetMemberVariable(task, "memberId", serviceScope);
            var memberStatus = await GetGenericVariable(task, "memberStatus", serviceScope);

            await GetMemberStatusUpdateCamundaService(serviceScope).EnableOrDisableMemberUsers(memberId, memberStatus);
        }

        private async Task EmailMemberToStateRemoval(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var memberId = await GetMemberVariable(task, "memberId", serviceScope);
            var memberStatus = await GetGenericVariable(task, "memberStatus", serviceScope);

            await GetMemberStatusUpdateCamundaService(serviceScope).EmailMemberToStateRemovalOrReactivation(memberId, memberStatus);
        }
        
        private static MemberStatusUpdateCamundaService GetMemberStatusUpdateCamundaService(IServiceScope serviceScope)
        {
            return serviceScope.ServiceProvider.GetRequiredService<MemberStatusUpdateCamundaService>();
        }
    }
}
