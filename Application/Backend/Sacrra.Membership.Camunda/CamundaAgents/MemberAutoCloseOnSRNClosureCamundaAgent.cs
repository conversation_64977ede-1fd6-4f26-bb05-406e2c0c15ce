using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Services;

namespace Sacrra.Membership.Camunda.CamundaAgents
{
    public class MemberAutoCloseOnSRNClosureCamundaAgent : BaseCamundaAgent
    {
        public MemberAutoCloseOnSRNClosureCamundaAgent(CamundaClient camundaClient) : base(camundaClient) { }

        public override async Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            switch (topicName)
            {
                case "kickoff-member-status-update-workflow":
                    await KickoffMemberStatusUpdateWorkflow(task, completeExternalTask, serviceScope);
                    break;
            }
            
            await task.Complete(completeExternalTask);
        }

        private async Task KickoffMemberStatusUpdateWorkflow(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var memberId = await GetIntegerVariable(task, "memberId", serviceScope);

            await GetMemberAutoCloseOnSRNClosureCamundaService(serviceScope).KickoffMemberStatusUpdateWorkflow(memberId);
        }
        
        private static MemberAutoCloseOnSRNClosureCamundaService GetMemberAutoCloseOnSRNClosureCamundaService(IServiceScope serviceScope)
        {
            return serviceScope.ServiceProvider.GetRequiredService<MemberAutoCloseOnSRNClosureCamundaService>();
        }
    }
}
