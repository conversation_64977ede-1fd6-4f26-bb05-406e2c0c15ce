using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Services;

namespace Sacrra.Membership.Camunda.CamundaAgents
{
    public class MemberAutoCloseOnSRNClosureCamundaAgent : BaseCamundaAgent
    {
        public MemberAutoCloseOnSRNClosureCamundaAgent(CamundaClient camundaClient) : base(camundaClient) { }

        public override async Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            switch (topicName)
            {
                case "kickoff-member-status-update-workflow":
                    KickoffMemberStatusUpdateWorkflow(task, completeExternalTask, serviceScope);
                    break;
            }
            
            task.Complete(completeExternalTask);
        }

        private void KickoffMemberStatusUpdateWorkflow(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
        {
            var memberId = GetIntegerVariable(task, "memberId", serviceScope);

            GetMemberAutoCloseOnSRNClosureCamundaService(serviceScope).KickoffMemberStatusUpdateWorkflow(memberId);
        }
        
        private static MemberAutoCloseOnSRNClosureCamundaService GetMemberAutoCloseOnSRNClosureCamundaService(IServiceScope serviceScope)
        {
            return serviceScope.ServiceProvider.GetRequiredService<MemberAutoCloseOnSRNClosureCamundaService>();
        }
    }
}
