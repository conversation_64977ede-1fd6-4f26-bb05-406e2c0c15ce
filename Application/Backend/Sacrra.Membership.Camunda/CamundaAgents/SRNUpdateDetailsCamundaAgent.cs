using System.Collections.Generic;
using System.Threading.Tasks;
using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Database.Models;

namespace Sacrra.Membership.Camunda.CamundaAgents;

public class SrnUpdateDetailsCamundaAgent: BaseCamundaAgent
{
    public SrnUpdateDetailsCamundaAgent(CamundaClient camundaClient) : base(camundaClient)
    {
    }

    public override async Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        switch (topicName)
        {
            case "notify-member-of-srn-details-change-decline":
                await NotifyMemberOfSrnDetailsChangeDecline(task, completeExternalTask, serviceScope);
                break;
            
            case "assign-srn-details-update-to-shm":
                await AssignSrnDetailsUpdateToShm(task, completeExternalTask, serviceScope);
                break;
            
            case "notify-shm-of-srn-update":
                await NotifyShmOfSrnUpdate(task, completeExternalTask, serviceScope);
                break;
            
            case "apply-srn-details-update":
                await ApplySrnDetailsUpdate(task, completeExternalTask, serviceScope);
                break;
        }

        await task.Complete(completeExternalTask);
    }

    private async Task ApplySrnDetailsUpdate(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var requestId = await GetMemberVariable(task, "ChangeRequestId", serviceScope);
        var changeRequest = await GetSrnUpdateDetailsCamundaService(serviceScope).GetMemberChangeRequest(requestId);

        if (changeRequest != null)
        {
            if (changeRequest.Type == ChangeObjectType.SRN)
            {
                var srnId = changeRequest.ObjectId;

                await GetSrnUpdateDetailsCamundaService(serviceScope).CamundaApplySRNChanges(srnId, changeRequest);
                await GetSrnUpdateDetailsCamundaService(serviceScope).NotifyApplicantOfSRNUpdateAccepted(srnId);
            }
        }
    }

    private async Task NotifyShmOfSrnUpdate(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetMemberVariable(task, "SRNId", serviceScope);

        if (srnId > 0)
        {
            await GetSrnUpdateDetailsCamundaService(serviceScope).NotifySHMAndSACRRAAdminOfSRNUpdate(srnId);
        }
    }

    private async Task AssignSrnDetailsUpdateToShm(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var requestId = await GetMemberVariable(task, "ChangeRequestId", serviceScope);
        var changeRequest = await GetCamundaRepository(serviceScope).GetMemberChangeRequest(requestId);

        if (changeRequest != null)
        {
            if (changeRequest.Type == ChangeObjectType.SRN)
            {
                var shmId = GetSrnUpdateDetailsCamundaService(serviceScope).GetSRNStakeholderManager(changeRequest.ObjectId);

                if (shmId > 0)
                {
                    completeExternalTask.Variables = new Dictionary<string, VariableValue>
                    {
                        ["stakeHolderManagerAssignee"] = VariableValue.FromObject(shmId.ToString())
                    };
                }
            }
        }
    }

    private async Task NotifyMemberOfSrnDetailsChangeDecline(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var requestId = await GetMemberVariable(task, "ChangeRequestId", serviceScope);
        var changeRequest = await GetSrnUpdateDetailsCamundaService(serviceScope).GetMemberChangeRequest(requestId);

        if (changeRequest != null)
        {
            if (changeRequest.Type == ChangeObjectType.SRN)
            {
                var srnId = changeRequest.ObjectId;

                await GetSrnUpdateDetailsCamundaService(serviceScope).NotifyApplicantOfSRNUpdateDecline(srnId);
            }
        }
    }
    
    private static SrnUpdateDetailsCamundaService GetSrnUpdateDetailsCamundaService(IServiceScope serviceScope)
    {
        return serviceScope.ServiceProvider.GetRequiredService<SrnUpdateDetailsCamundaService>();
    }
}