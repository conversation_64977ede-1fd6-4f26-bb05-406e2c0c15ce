using System;
using System.Threading.Tasks;
using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Services;

namespace Sacrra.Membership.Camunda.CamundaAgents;

public class SrnStatusUpdateNonCancellationsCamundaAgent: BaseCamundaAgent
{
    public SrnStatusUpdateNonCancellationsCamundaAgent(CamundaClient camundaClient) : base(camundaClient)
    {
    }

    public override async Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        switch (topicName)
        {
            case "update-srn-status-for-non-cancellations":
                await UpdateSrnStatusForNonCancellations(task, completeExternalTask, serviceScope);
                break;
            
            case "email-member-about-srn-status-update-for-non-cancellations":
                await EmailMemberAboutSrnStatusUpdateForNonCancellations(task, completeExternalTask, serviceScope);
                break;
            
            case "email-bureaus-about-srn-status-update-for-non-cancellations":
                await EmailBureausAboutSrnStatusUpdateForNonCancellations(task, completeExternalTask, serviceScope);
                break;
        }

        await task.Complete(completeExternalTask);
    }

    private async Task EmailBureausAboutSrnStatusUpdateForNonCancellations(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetGenericVariable(task, "SRNId", serviceScope);
        
        if (!string.IsNullOrEmpty(srnId))
        {
            await GetSrnStatusUpdateNonCancellationsCamundaService(serviceScope).EmailBureausAboutSRNStatusUpdateForNonCancellations(Convert.ToInt32(srnId));
        }
    }

    private async Task EmailMemberAboutSrnStatusUpdateForNonCancellations(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetGenericVariable(task, "SRNId", serviceScope);
        
        if (!string.IsNullOrEmpty(srnId))
        {
            await GetSrnStatusUpdateNonCancellationsCamundaService(serviceScope).EmailMemberAboutSRNStatusUpdateForNonCancellations(Convert.ToInt32(srnId));
        }
    }

    private async Task UpdateSrnStatusForNonCancellations(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetGenericVariable(task, "SRNId", serviceScope);
        var srnStatusName = await GetGenericVariable(task, "SRNStatusName", serviceScope);
        var updatedByUserIdVariable = await GetGenericVariable(task, "UpdatedByUserId", serviceScope);

        if (!string.IsNullOrEmpty(srnId) && !string.IsNullOrEmpty(srnStatusName))
        {
            int updatedByUserId = (!string.IsNullOrEmpty(updatedByUserIdVariable)) ? Convert.ToInt32(updatedByUserIdVariable) : 0;

            var taskInfo = await task.Get();
            var processInstanceId = taskInfo.ProcessInstanceId;

            await GetSrnStatusUpdateNonCancellationsCamundaService(serviceScope).UpdateSRNStatus(Convert.ToInt32(srnId), srnStatusName, updatedByUserId, processInstanceId);
        }
    }
    
    private static SrnStatusUpdateNonCancellationsCamundaService GetSrnStatusUpdateNonCancellationsCamundaService(IServiceScope serviceScope)
    {
        return serviceScope.ServiceProvider.GetRequiredService<SrnStatusUpdateNonCancellationsCamundaService>();
    }
}