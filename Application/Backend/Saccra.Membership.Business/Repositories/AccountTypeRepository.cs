using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources.AccountType;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Repositories
{
    public class AccountTypeRepository
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }

        public AccountTypeRepository(AppDbContext dbContext, IMapper mapper)
        {
            this._mapper = mapper;
            this._dbContext = dbContext;
        }

        public AccountTypeGetResource Get(int id)
        {
            var selectRecord = _dbContext.Set<AccountType>()
                .AsNoTracking()
                .FirstOrDefaultAsync(s => s.Id == id);

            var returnRecord = _mapper.Map<AccountTypeGetResource>(selectRecord);

            return returnRecord;
        }

        public async Task<PagedList<IdValuePairResource>> List(NameListParams listParams)
        {
            var query = _dbContext.Set<AccountType>()
                .AsQueryable();
            if (listParams != null)
            {
                if (listParams.Name != null)
                    query = query.Where(u => u.Name.ToLower().Contains(listParams.Name.ToLower()));
            }
            if (listParams.SortDirection == "asc")
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.Name);
                        break;
                }
            }
            else
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.Name);
                        break;
                }
            }

            var count = query.Count();
            var pageNumber = listParams.PageNumber;
            if (count / listParams.PageSize < listParams.PageNumber)
                pageNumber = (count / listParams.PageSize) + 1;

            var queryItems = query.Skip((pageNumber - 1) * listParams.PageSize).Take(listParams.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            itemsToReturn = itemsToReturn.OrderBy(x => x.Value).ToList();

            return new PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.PageSize);
        }
        public AccountTypeGetResource Update(AccountTypeUpdateResource modelForUpdate)
        {
            var model = _mapper.Map<AccountType>(modelForUpdate);

            _dbContext.Set<AccountType>().Update(model);

            _dbContext.SaveChanges();

            return Get(model.Id);
        }

        public int Create(AccountTypeCreateResource modelForCreate)
        {
            var model = _mapper.Map<AccountType>(modelForCreate);

            _dbContext.Set<AccountType>().AddAsync(model);

            _dbContext.SaveChanges();

            return model.Id;
        }
        public void Delete(int id)
        {
            var entity = _dbContext.Set<AccountType>().FindAsync(id);

            _dbContext.Set<AccountType>().Remove(entity);
            _dbContext.SaveChanges();
        }
    }
}