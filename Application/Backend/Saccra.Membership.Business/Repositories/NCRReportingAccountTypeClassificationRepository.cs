using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Business.Resources.NCRReportingAccountTypeClassification;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Repositories
{
    public class NCRReportingAccountTypeClassificationRepository
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }

        public NCRReportingAccountTypeClassificationRepository(AppDbContext dbContext, IMapper mapper)
        {
            this._mapper = mapper;
            this._dbContext = dbContext;
        }

        public async Task<NCRReportingAccountTypeClassificationGetResource> Get(int id)
        {
            var selectRecord = await _dbContext.Set<NCRReportingAccountTypeClassification>()
                .AsNoTracking()
                .FirstOrDefaultAsync(s => s.Id == id);

            var returnRecord = _mapper.Map<NCRReportingAccountTypeClassificationGetResource>(selectRecord);

            return returnRecord;
        }

        public async Task<PagedList<IdValuePairResource>> List(NameListParams listParams)
        {
            var query = _dbContext.Set<NCRReportingAccountTypeClassification>()
                    .AsQueryable();
            if (listParams != null)
            {
                if (listParams.Name != null)
                    query = query.Where(u => u.Name.ToLower().Contains(listParams.Name.ToLower()));
            }
            if (listParams.SortDirection == "asc")
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.Name);
                        break;
                }
            }
            else
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.Name);
                        break;
                }
            }

            var count = await query.CountAsync();
            var pageNumber = listParams.PageNumber;
            if (count / listParams.PageSize < listParams.PageNumber)
                pageNumber = (count / listParams.PageSize) + 1;

            var queryItems = query.Skip((pageNumber - 1) * listParams.PageSize).Take(listParams.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            itemsToReturn = itemsToReturn.OrderBy(x => x.Value).ToList();

            return new PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.PageSize);
        }
        public async Task<NCRReportingAccountTypeClassificationGetResource> Update(NCRReportingAccountTypeClassificationUpdateResource modelForUpdate)
        {
            var model = _mapper.Map<NCRReportingAccountTypeClassification>(modelForUpdate);

            _dbContext.Set<NCRReportingAccountTypeClassification>().Update(model);

            await _dbContext.SaveChangesAsync();

            return await Get(model.Id);
        }

        public async Task<int> Create(NCRReportingAccountTypeClassificationCreateResource modelForCreate)
        {
            var model = _mapper.Map<NCRReportingAccountTypeClassification>(modelForCreate);

            await _dbContext.Set<NCRReportingAccountTypeClassification>().AddAsync(model);

            await _dbContext.SaveChangesAsync();

            return model.Id;
        }
        public async Task Delete(int id)
        {
            var entity = await _dbContext.Set<NCRReportingAccountTypeClassification>().FindAsync(id);

            _dbContext.Set<NCRReportingAccountTypeClassification>().Remove(entity);
            await _dbContext.SaveChangesAsync();
        }
    }
}
