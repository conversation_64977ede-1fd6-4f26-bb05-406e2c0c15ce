using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Business.Resources.LoanManagementSystemVendor;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Repositories
{
    public class LoanManagementSystemVendorRepository
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }

        public LoanManagementSystemVendorRepository(AppDbContext dbContext, IMapper mapper)
        {
            this._mapper = mapper;
            this._dbContext = dbContext;
        }

        public async Task<LoanManagementSystemVendorGetResource> Get(int id)
        {
            var selectRecord = await _dbContext.Set<LoanManagementSystemVendor>()
                .AsNoTracking()
                .FirstOrDefaultAsync(s => s.Id == id);

            var returnRecord = _mapper.Map<LoanManagementSystemVendorGetResource>(selectRecord);

            return returnRecord;
        }

        public async Task<PagedList<IdValuePairResource>> List(NameListParams listParams)
        {
            var query = _dbContext.Set<LoanManagementSystemVendor>()
                .Where(x => !string.IsNullOrEmpty(x.Name))
                .AsQueryable();
            if (listParams != null)
            {
                if (listParams.Name != null)
                    query = query.Where(u => u.Name.ToLower().Contains(listParams.Name.ToLower()));
            }
            if (listParams.SortDirection == "asc")
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderBy(u => u.Name);
                        break;
                }
            }
            else
            {
                switch (listParams.SortBy)
                {
                    default:
                        query = query.OrderByDescending(u => u.Name);
                        break;
                }
            }

            var count = await query.CountAsync();
            var pageNumber = listParams.PageNumber;
            if (count / listParams.PageSize < listParams.PageNumber)
                pageNumber = (count / listParams.PageSize) + 1;

            var queryItems = query.Skip((pageNumber - 1) * listParams.PageSize).Take(listParams.PageSize).ToList();
            var itemsToReturn = _mapper.Map<IEnumerable<IdValuePairResource>>(queryItems).ToList();

            itemsToReturn = itemsToReturn.OrderBy(x => x.Value).ToList();
            itemsToReturn.Insert(0, new IdValuePairResource { Id = 0, Value = "None" });

            return new PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.PageSize);
        }
        public async Task<LoanManagementSystemVendorGetResource> Update(LoanManagementSystemVendorUpdateResource modelForUpdate)
        {
            var model = _mapper.Map<LoanManagementSystemVendor>(modelForUpdate);

            _dbContext.Set<LoanManagementSystemVendor>().Update(model);

            await _dbContext.SaveChangesAsync();

            return await Get(model.Id);
        }

        public async Task<int> Create(LoanManagementSystemVendorCreateResource modelForCreate)
        {
            var model = _mapper.Map<LoanManagementSystemVendor>(modelForCreate);

            await _dbContext.Set<LoanManagementSystemVendor>().AddAsync(model);

            await _dbContext.SaveChangesAsync();

            return model.Id;
        }
        public async Task Delete(int id)
        {
            var entity = await _dbContext.Set<LoanManagementSystemVendor>().FindAsync(id);

            _dbContext.Set<LoanManagementSystemVendor>().Remove(entity);
            await _dbContext.SaveChangesAsync();
        }
    }
}
