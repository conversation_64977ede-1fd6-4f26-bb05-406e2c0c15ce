using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using System.Linq;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Repositories
{
    public class ALGRepository
    {
        private readonly AppDbContext _dbContext;
        public IMapper _mapper { get; }

        public ALGRepository(AppDbContext dbContext, IMapper mapper)
        {
            this._mapper = mapper;
            this._dbContext = dbContext;
        }

        public ALGGetResource Get(int id)
        {
            var selectRecord = _dbContext.Set<ALG>()
                .AsNoTracking()
                .FirstOrDefaultAsync(s => s.Id == id);

            var returnRecord = _mapper.Map<ALGGetResource>(selectRecord);

            return returnRecord;
        }

        public async Task<PagedList<IdValuePairResource>> List(NameListParams listParams)
        {
            var query = _dbContext.Set<Member>()
                    .AsQueryable();
            
            query = query.Where(u => u.MembershipTypeId == MembershipTypes.ALGLeader);

            var count = query.Count();
            var pageNumber = listParams.PageNumber;

            if (count / listParams.PageSize < listParams.PageNumber)
                pageNumber = (count / listParams.PageSize) + 1;

            var itemsToReturn = query.Select(x => new IdValuePairResource() { Id = x.Id, Value = x.RegisteredName }).ToList();

            return new PagedList<IdValuePairResource>(itemsToReturn, count, pageNumber, listParams.PageSize);
        }

        public ALGGetResource Update(ALGUpdateResource modelForUpdate)
        {
            var model = _mapper.Map<ALG>(modelForUpdate);

            _dbContext.Set<ALG>().Update(model);

            _dbContext.SaveChanges();

            return Get(model.Id);
        }

        public int Create(ALGCreateResource modelForCreate)
        {
            var model = _mapper.Map<ALG>(modelForCreate);

            _dbContext.Set<ALG>().AddAsync(model);

            _dbContext.SaveChanges();

            return model.Id;
        }
        public void Delete(int id)
        {
            var entity = _dbContext.Set<ALG>().FindAsync(id);

            _dbContext.Set<ALG>().Remove(entity);
            _dbContext.SaveChanges();
        }
    }
}
