using AutoMapper;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using RestSharp;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Data;
using System.Linq;
using System.Net.Http;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using RestSharp.Authenticators;
using System.Diagnostics;
using Sacrra.Membership.Business.DTOs.UserAdminDTOs;
using Sacrra.Membership.Business.DTOs.UserDTOs;

namespace Sacrra.Membership.Business.Services.UserAdminService
{
    public class UserAdminService
    {
        private readonly AppDbContext _dbContext;
        private readonly GlobalHelper _globalHelper;
        private readonly UserAdminServiceHelper _userAdminServiceHelper;
        private readonly AuthenticationService _authService;
        private readonly AuthRepository _authRepository;
        public IMapper _mapper { get; }

        public UserAdminService(AppDbContext dbContext, GlobalHelper globalHelper, IMapper mapper, UserAdminServiceHelper userAdminServiceHelper, AuthenticationService authenticationService, AuthRepository authRepositiory)
        {
            _dbContext = dbContext;
            _globalHelper = globalHelper;
            _mapper = mapper;
            _userAdminServiceHelper = userAdminServiceHelper;
            _authService = authenticationService;
            _authRepository = authRepositiory;
        }

        public List<UserAdminDTO> GetAllUsersForAdminScreen() 
        {
            //var users = new List<UserAdminDTO>();
            //var dbUserList = _dbContext.Users
            //    .ToList();
            //var token = Helpers.GetAuth0APIToken(_auth0APIManagementSettings);
            //var auth0UserList = GetUserAuth0UserList(token);

            //foreach (var user in dbUserList) 
            //{
            //    users.Add(new UserAdminDTO() {
            //        UserId = user.Id,
            //        UserEmail = user.Email,
            //        UserFirstName = user.FirstName,
            //        UserLastName = user.LastName,
            //        UserRole = EnumHelper.GetEnumIdValuePair<UserRoles>((int)user.RoleId)?.Value,
            //        DateOfUserCreation = user.DateCreated,
            //        DateTermsAndConditionsRead = user.LastReadTsAndCsAt,
            //        UserStatus = auth0UserList.Find(x => x.user_id == user.Auth0Id)?.blocked == null || auth0UserList.Find(x => x.user_id == user.Auth0Id).blocked == false ? "Active" : "Blocked"
            //    });
            //}

            //return users;
            throw new NotImplementedException();
        }

        public List<Auth0UserDTO> GetUserAuth0UserList(string token)
        {
            //var uri = "https://" + _auth0APIManagementSettings.Domain + "/api/v2/users";
            //var restClient = new RestClient(uri)
            //{
            //    Authenticator = new JwtAuthenticator(token)
            //};
            //var request = new RestRequest();
            //var response = restClient.Get(request);

            //if (response.StatusCode != HttpStatusCode.OK)
            //{
            //    throw new Exception("Unable to fetch user list from Auth0." + response.Content);
            //}

            //return JsonConvert.DeserializeObject<List<Auth0UserDTO>>(response.Content);
            throw new NotImplementedException();
        }

        public string GetPredictedUserRole(int memberId)
        {
            var selectedMember = _dbContext.Members
                .Where(x => x.Id == memberId)
                .FirstOrDefault();

            var membershipType = selectedMember.MembershipTypeId switch
            {
                MembershipTypes.FullMember => "Member",
                MembershipTypes.NonMember => "Member",
                MembershipTypes.ALGClient => "ALG Leader",
                MembershipTypes.Affiliate => "N/A",
                MembershipTypes.Bureau => "Bureau",
                MembershipTypes.ALGLeader => "ALG Leader",
                _ => "N/A",
            };

            return JsonConvert.SerializeObject(membershipType);
        }

        public List<UserAdminExportDTO> ExportUserAdminScreen()
        {
            var query = _dbContext.Set<User>()
                    .Include(i => i.MembersIManage)
                    .AsQueryable();

            var realItems = new List<UserAdminExportDTO>();

            var queryItems = query.ToList();

            foreach (var item in queryItems)
            {
                var exportItem = new UserAdminExportDTO()
                {
                    MemberCategoryType = (item.MembersIManage.Count > 0) ? EnumHelper.GetEnumIdValuePair<MembershipTypes>((int)item.MembersIManage.First().MembershipTypeId).Value: null,
                    MembershipStatus = (item.MembersIManage.Count > 0) ? item.MembersIManage.First().StatusComment: null,
                    CompanyName = (item.MembersIManage.Count > 0) ? item.MembersIManage.First().AnalyticsCompanyName: null,
                    CompanyRegistrationNumber = (item.MembersIManage.Count > 0) ? item.MembersIManage.First().RegisteredNumber: null,
                    IdentificationNumber = (item.MembersIManage.Count > 0) ? item.MembersIManage.First().IdNumber : null,
                    StakeholderManager = (item.MembersIManage.Count > 0) ? item.MembersIManage.First().StakeholderManager.FullName : null,
                    ApplicationStatus =  (item.MembersIManage.Count > 0) ?EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)item.MembersIManage.First().ApplicationStatusId).Value : null,
                    UserFirstName = item.FirstName,
                    UserLastName = item.LastName,
                    UserEmail = item.Email,
                    DateOfUserCreation = item.DateCreated,
                    UserRole = EnumHelper.GetEnumIdValuePair<UserRoles>((int)item.RoleId).Value,
                    DateTermsAndConditionsRead = item.LastReadTsAndCsAt,
                    DateTermsAndConditionsAccepted = null,
                    UserStatus = null,
                };

                realItems.Add(exportItem);
            }

            realItems = realItems.OrderBy(x => x.UserRole).ToList();

            return realItems;
        }

        public void AddUpdateUser(UserAdminInputDTO user)
        {
            var dbUser = _dbContext.Users.FirstOrDefault(i => i.Id == user.Id);
            var dbMember = _dbContext.Members.FirstOrDefault(i => i.Id == user.MemberId);

            if (dbUser == null)
            {
                dbUser = new User()
                {
                    Email = user.Email,
                    IsEmailConfirmed = false,
                    RequirePasswordChange = true,
                    DateCreated = DateTime.Now
                };
                _dbContext.Users.Add(dbUser);

                var userResource = new UserCreateSimpleResource
                {
                    FirstName = user.Email,
                    LastName = user.Email,
                    Email = user.Email,
                    Password = ""
                };
                _authRepository.CreateAuth0User(userResource).Wait();
                _dbContext.SaveChanges();
            }

            // Link User to Member based off of MembershipTypeId
            int newRoleId;
            switch (EnumHelper.GetEnumIdValuePair<UserRoles>((int)dbMember.MembershipTypeId).Id)
            {
                case 1:
                    break;

                case 2:
                    break;

                case 3:
                    break;

                case 4:
                    break;

                case 5:
                    break;

                case 6:
                    break;

                default:
                    break;
            }
            _dbContext.SaveChanges();
        }
        
        public void CreateNewSHM()
        {
            using (var conn = new SqlConnection("Server=sacrra-automation-prod-db-server.database.windows.net;User=ewx;Password=********************;Database=Sacrra.Membership;"))
            using (var command = new SqlCommand("ProcedureName", conn)
            {
                CommandType = CommandType.StoredProcedure
            })
            {
                conn.Open();
                command.ExecuteNonQuery();
            }
        }
    }
}
