using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Services.DataWarehouseService;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;
using Sacrra.Membership.Reporting.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using Sacrra.Membership.Business.DTOs.AdhocRplacementScheduleDTOs;
using Sacrra.Membership.Database.Migrations;
using AdhocFileSchedule = Sacrra.Membership.Database.Models.AdhocFileSchedule;

namespace Sacrra.Membership.Business.Services.AdhocReplacementScheduleService
{
    public class AdhocReplacementScheduleService
    {
        private AppDbContext _dbContext;
        private readonly ConfigSettings _configSettings;
        private readonly EmailService _emailService;
        private DataWarehouseService.DataWarehouseService _dataWarehouseService;
        private ReportTables _reportTables;
        public IMapper _mapper { get; }

        public AdhocReplacementScheduleService(AppDbContext dbContext, IOptions<ConfigSettings> configSettings, IMapper mapper, EmailService emailService, DataWarehouseService.DataWarehouseService dataWarehouseService)
        {
            _dbContext = dbContext;
            _configSettings = configSettings.Value;
            _emailService = emailService;
            _mapper = mapper;
            _dataWarehouseService = dataWarehouseService;
            _reportTables = _dataWarehouseService.ReportTables;
        }

        public List<AdhocReplacementScheduleOutputDTO> GetAdhocFilesSchedule(ClaimsPrincipal user)
        {
            try
            {
                var auth0User = Helpers.Helpers.GetLoggedOnUserNonAsync(_dbContext, user);
                var currentUser = _dbContext.Users
                    .Where(x => x.Auth0Id == auth0User.Auth0Id)
                    .Include(x => x.Members)
                    .ThenInclude(x => x.Member)
                    .FirstOrDefault();

                var baseQuery = _dbContext.AdhocFileSchedules
                    .Include(x => x.AdhocFileSubmission)
                        .ThenInclude(x => x.SRN)
                    .Include(x => x.AdhocFileSubmission.Member)
                        .ThenInclude(x => x.StakeholderManager)
                    .Include(x => x.AdhocFileSubmission.FileSubmissionReason)
                    .Include(x => x.Bureau)
                    .Include(x => x.UnnsuccessfulLoadReason)
                    .AsQueryable();

                List<AdhocFileSchedule> adhocFileScheduleList;

                switch (auth0User.RoleId)
                {
                    case UserRoles.StakeHolderManager:
                    case UserRoles.StakeHolderAdministrator:
                    case UserRoles.FinancialAdministrator:
                    case UserRoles.SACRRAAdministrator:
                    case UserRoles.SystemAdministrator:
                        adhocFileScheduleList = baseQuery.Where(x => x.Id != null).ToList();
                        break;

                    case UserRoles.Member:
                        var currentMemberIds = currentUser.Members.Select(m => m.Member.Id).ToList();
                        adhocFileScheduleList = baseQuery
                            .Where(x => currentMemberIds.Contains(x.AdhocFileSubmission.MemberId))
                            .ToList();
                        break;

                    case UserRoles.Bureau:
                        var bureauMemberIds = currentUser.Members.Select(m => m.Member.Id).ToList();
                        adhocFileScheduleList = baseQuery
                            .Where(x => bureauMemberIds.Contains(x.BureauId ?? 0))
                            .ToList();
                        break;

                    case UserRoles.ALGLeader:
                        var algLeaderMemberIds = currentUser.Members.Select(m => m.MemberId).ToList();
                        adhocFileScheduleList = baseQuery
                            .Where(x => x.Id != null && algLeaderMemberIds.Contains(x.AdhocFileSubmission.MemberId))
                            .ToList();
                        break;

                    case UserRoles.User:
                        throw new UnauthorizedException();

                    default:
                        adhocFileScheduleList = new List<AdhocFileSchedule>();
                        break;
                }

                if (!adhocFileScheduleList.Any())
                {
                    return new List<AdhocReplacementScheduleOutputDTO>();
                }

                var submissionIds = adhocFileScheduleList.Select(x => x.AdhocFileSubmissionId).Distinct().ToList();
                var reasonIds = adhocFileScheduleList
                    .Where(x => x.UnnsuccessfulLoadReasonId.HasValue)
                    .Select(x => x.UnnsuccessfulLoadReasonId.Value)
                    .Distinct().ToList();

                var bureauLoadStatsRaw = _dbContext.BureauLoadStats
                    .Where(x => submissionIds.Contains(x.AdHocFileSubmissionId))
                    .ToList();

                // 🛡 Prevent dictionary duplication error
                var successfulBureauLoadStats = bureauLoadStatsRaw
                    .Where(x => reasonIds.Contains(x.AdHocFileSubmissionId))
                    .GroupBy(x => x.AdHocFileSubmissionId)
                    .Select(g => g.First())
                    .ToDictionary(x => x.AdHocFileSubmissionId, x => (int?)x.Id);

                var bureauLoadStats = bureauLoadStatsRaw
                    .GroupBy(x => x.AdHocFileSubmissionId)
                    .Where(g => g.Any())
                    .ToDictionary(g => g.Key, g => g.ToList());

                var unsuccessfulLoadReasons = _dbContext.ReplacementFileSubmissionReasons
                    .Where(x => reasonIds.Contains(x.Id))
                    .AsEnumerable()
                    .GroupBy(x => x.Id)
                    .Select(g => g.First())
                    .ToDictionary(x => x.Id, x => x.Name);


                var adhocFileScheduleOutputDTOList = new List<AdhocReplacementScheduleOutputDTO>();

                foreach (var adhocFileSchedule in adhocFileScheduleList)
                {
                    var submission = adhocFileSchedule.AdhocFileSubmission;
                    var member = submission.Member;
                    BureauLoadStats loadStats = null;

                    if (bureauLoadStats.TryGetValue(submission.Id, out var loadStatsList))
                    {
                        loadStats = loadStatsList.FirstOrDefault();
                    }

                    unsuccessfulLoadReasons.TryGetValue(adhocFileSchedule.UnnsuccessfulLoadReasonId ?? 0, out var unsuccessfulReason);

                    int? bureauLoadStatsId = null;
                    if (adhocFileSchedule.AdhocFileBureauStatusId == ReplacementFileBureauStatuses.BureauLoadSuccessful)
                    {
                        successfulBureauLoadStats.TryGetValue(adhocFileSchedule.UnnsuccessfulLoadReasonId ?? 0, out bureauLoadStatsId);
                    }
                    
                    var replacementFileAlgLeader = _dbContext.ALGClientLeaders
                        .Where(x => x.LeaderId == submission.SRN.ALGLeaderId)
                        .Select(x => x.Leader.RegisteredName)
                        .AsNoTracking()
                        .FirstOrDefault();

                    var dto = new AdhocReplacementScheduleOutputDTO
                    {
                        Id = adhocFileSchedule.Id,
                        FileSubmissionReasonConstId = submission.FileSubmissionReason?.FileSubmissionReasonConstId ?? 0,
                        BureauLoadStatsId = bureauLoadStatsId,
                        ActualSubmissionDate = submission.ActualSubmissionDate?.ToString("yyyy/MM/dd") ?? "N/A",
                        AdHocFileSubmissionId = submission.Id,
                        AlgLeader = replacementFileAlgLeader ?? "N/A",
                        BureauReasonForUnsuccessfulLoad = unsuccessfulReason ?? "N/A",
                        MemberName = member.RegisteredName,
                        NumberOfRecords = submission.NumberOfRecords,
                        ProposedSubmissionDate = submission.PlannedSubmissionDate.ToString("yyyy/MM/dd"),
                        ReplacementFileName = submission.FileName,
                        FileName = submission.FileName,
                        ReSubmissionReason = submission.FileSubmissionReason?.Name,
                        SACRRAAccountType = submission.SACRRAAccountType,
                        SACRRAIndustry = EnumHelper.GetEnumIdValuePair<IndustryClassifications>((int)member.IndustryClassificationId).Value,
                        SRNDisplayName = submission.SRN?.TradingName ?? "N/A",
                        SRNNumber = submission.SRN?.SRNNumber ?? "N/A",
                        StakeholderManager = member.StakeholderManager?.FullName,
                        SubmissionStatus = EnumHelper.GetEnumIdValuePair<ReplacementFileSubmissionStatuses>(submission.AdhocFileSubmissionStatusId).Value,
                        SubmissionStatusDate = submission.SubmissionStatusDate.ToString("yyyy/MM/dd"),
                        BureauLoadStatus = adhocFileSchedule.AdhocFileBureauStatusId == null
                            ? "N/A"
                            : EnumHelper.GetEnumIdValuePair<ReplacementFileBureauStatuses>((int)adhocFileSchedule.AdhocFileBureauStatusId).Value,
                        BureauName = adhocFileSchedule.Bureau?.RegisteredName ?? "N/A",
                        RequestDate = submission.CreatedAt.ToString("yyyy/MM/dd"),
                        Comments = string.IsNullOrWhiteSpace(adhocFileSchedule.AdhocFileSubmission.Comments) ? "N/A" : adhocFileSchedule.AdhocFileSubmission.Comments,
                    };

                    if (loadStats != null)
                    {
                        dto.NumberOfRecordsReceived = loadStats.NumberOfRecordsReceived;
                        dto.NumberOfRecordsMatched = loadStats.NumberOfRecordsMatched;
                        dto.NumberOfRecordsMatchedButNotUpdated = loadStats.NumberOfRecordsMatchedButNotUpdated;
                        dto.NumberOfRecordsMatchedAndUpdated = loadStats.NumberOfRecordsMatchedAndUpdated;
                        dto.NumberOfRecordsUnmatched = loadStats.NumberOfRecordsUnmatched;
                        dto.TotalNumberOfQE1RecordRemainingOnDBPostCleanup = loadStats.TotalNumberOfQE1RecordRemainingOnDBPostCleanup;
                        dto.DateNewQE1ExtractSharedPostCleanup = loadStats.DateNewQE1ExtractSharedPostCleanup;
                        dto.NumberOfDuplicatesRemovedFromDBBasedOnExtract = loadStats.NumberOfDuplicatesRemovedFromDBBasedOnExtract;
                        dto.NumberOfRecordsMigrated = loadStats.NumberOfRecordsMigrated;
                        dto.NumberOfRecordsMergedAcrossSRNs = loadStats.NumberOfRecordsMergedAcrossSRNs;
                        dto.NumberOfRecordsMergedWithinSRN = loadStats.NumberOfRecordsMergedWithinSRN;
                        dto.NumberOfRecordsMatchedSuccessfullyConverted = loadStats.NumberOfRecordsMatchedSuccessfullyConverted;
                    }

                    adhocFileScheduleOutputDTOList.Add(dto);
                }

                return adhocFileScheduleOutputDTOList;
            }
            catch (Exception ex)
            {
                throw new ReplacementFileScheduleException(0, null, ex.StackTrace);
            }
        }


        public List<AdhocReplacementScheduleOutputDTO> GetReplacementFilesSchedule(ClaimsPrincipal user)
        {
            try
            {
                var auth0User = Helpers.Helpers.GetLoggedOnUserNonAsync(_dbContext, user);
                var currentUser = _dbContext.Users
                    .AsNoTracking()
                    .Include(u => u.Members)
                    .FirstOrDefault(u => u.Auth0Id == auth0User.Auth0Id);

                if (currentUser == null)
                    return new List<AdhocReplacementScheduleOutputDTO>();

                var memberIds = currentUser.Members.Select(m => m.MemberId).ToList();

                var query = _dbContext.ReplacementFileSchedule.AsNoTracking();

                // Apply role-based filtering
                if (auth0User.RoleId == UserRoles.Member)
                {
                    var memberId = memberIds.FirstOrDefault();
                    if (memberId == 0) return new List<AdhocReplacementScheduleOutputDTO>();
                    query = query.Where(x => x.ReplacementFileSubmission.MemberId == memberId);
                }
                else if (auth0User.RoleId == UserRoles.Bureau)
                {
                    var bureauId = memberIds.FirstOrDefault();
                    if (bureauId == 0) return new List<AdhocReplacementScheduleOutputDTO>();
                    query = query.Where(x => x.BureauId == bureauId);
                }
                else if (auth0User.RoleId == UserRoles.ALGLeader)
                {
                    query = query.Where(x => memberIds.Contains(x.ReplacementFileSubmission.MemberId));
                }
                else if (auth0User.RoleId == UserRoles.User)
                {
                    throw new UnauthorizedException();
                }

                var scheduleData = query
                    .Select(schedule => new
                    {
                        schedule.Id,
                        schedule.BureauId,
                        BureauName = schedule.Bureau.RegisteredName,
                        UnsuccessfulReason = schedule.UnnsuccessfulLoadReason.Name,
                        schedule.ReplacementFileBureauStatusId,

                        SubmissionId = schedule.ReplacementFileSubmission.Id,
                        ReplacementFileName = schedule.ReplacementFileSubmission.ReplacementFileName,
                        ActualSubmissionDate = schedule.ReplacementFileSubmission.ActualSubmissionDate,
                        PlannedSubmissionDate = schedule.ReplacementFileSubmission.PlannedSubmissionDate,
                        NumberOfRecords = schedule.ReplacementFileSubmission.NumberOfRecords,
                        SACRRAAccountType = schedule.ReplacementFileSubmission.SACRRAAccountType,
                        ReplacementFileSubmissionStatusId = schedule.ReplacementFileSubmission.ReplacementFileSubmissionStatusId,
                        SubmissionStatusDate = schedule.ReplacementFileSubmission.SubmissionStatusDate,
                        ReplacementFileSubmissionReasonName = schedule.ReplacementFileSubmission.ReplacementFileSubmissionReason.Name,

                        MemberId = schedule.ReplacementFileSubmission.Member.Id,
                        MemberRegisteredName = schedule.ReplacementFileSubmission.Member.RegisteredName,
                        IndustryClassificationId = schedule.ReplacementFileSubmission.Member.IndustryClassificationId,
                        StakeholderManagerFullName = schedule.ReplacementFileSubmission.Member.StakeholderManager.FullName,
                        ALGLeaderId = schedule.ReplacementFileSubmission.SRN.ALGLeaderId,
                        SRNTradingName = schedule.ReplacementFileSubmission.SRN.TradingName,
                        SRNNumber = schedule.ReplacementFileSubmission.SRN.SRNNumber
                    })
                    .ToList();

                if (!scheduleData.Any())
                    return new List<AdhocReplacementScheduleOutputDTO>();

                var submissionIds = scheduleData.Select(s => s.SubmissionId).Distinct().ToList();
                var adhocSubmissionsDict = _dbContext.AdhocFileSubmissions
                    .AsNoTracking()
                    .Where(x => submissionIds.Contains(x.Id))
                    .Include(x => x.SRN)
                    .ToDictionary(x => x.Id, x => x.CreatedAt);

                var result = new List<AdhocReplacementScheduleOutputDTO>();

                foreach (var schedule in scheduleData)
                {
                    var replacementFileAlgLeader = _dbContext.ALGClientLeaders
                        .Where(x => x.LeaderId == schedule.ALGLeaderId)
                        .Select(x => x.Leader.RegisteredName)
                        .AsNoTracking()
                        .FirstOrDefault();
                    
                    result.Add(new AdhocReplacementScheduleOutputDTO
                    {
                        ActualSubmissionDate = schedule.ActualSubmissionDate?.ToString("yyyy/MM/dd") ?? "N/A",
                        AlgLeader = replacementFileAlgLeader,
                        BureauReasonForUnsuccessfulLoad = schedule.UnsuccessfulReason ?? "N/A",
                        MemberName = schedule.MemberRegisteredName,
                        NumberOfRecords = schedule.NumberOfRecords,
                        ProposedSubmissionDate = schedule.PlannedSubmissionDate.ToString("yyyy/MM/dd"),
                        ReplacementFileName = schedule.ReplacementFileName,
                        ReSubmissionReason = schedule.ReplacementFileSubmissionReasonName ?? "N/A",
                        SACRRAAccountType = schedule.SACRRAAccountType,
                        SACRRAIndustry = EnumHelper.GetEnumIdValuePair<IndustryClassifications>((int)schedule.IndustryClassificationId).Value,
                        SRNDisplayName = schedule.SRNTradingName ?? "N/A",
                        SRNNumber = schedule.SRNNumber ?? "N/A",
                        StakeholderManager = schedule.StakeholderManagerFullName ?? "N/A",
                        SubmissionStatus = EnumHelper.GetEnumIdValuePair<ReplacementFileSubmissionStatuses>(schedule.ReplacementFileSubmissionStatusId).Value,
                        SubmissionStatusDate = schedule.SubmissionStatusDate.ToString("yyyy/MM/dd"),
                        BureauLoadStatus = schedule.ReplacementFileBureauStatusId.HasValue
                            ? EnumHelper.GetEnumIdValuePair<ReplacementFileBureauStatuses>((int)schedule.ReplacementFileBureauStatusId.Value).Value
                            : "N/A",
                        BureauName = schedule.BureauName ?? "N/A",
                        RequestDate = adhocSubmissionsDict.ContainsKey(schedule.SubmissionId)
                            ? adhocSubmissionsDict[schedule.SubmissionId].ToString("yyyy/MM/dd")
                            : "N/A"
                    });
                }

                return result;
            }
            catch (Exception ex)
            {
                throw new ReplacementFileScheduleException(0, null, ex.StackTrace);
            }
        }
    }
}




