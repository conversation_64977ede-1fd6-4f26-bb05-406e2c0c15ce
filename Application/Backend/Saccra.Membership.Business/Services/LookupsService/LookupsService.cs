using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Sacrra.Membership.Business.DTOs.ReplacementFileSubmissionDTOs;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.DTOs.AdhocFileSubmissionDTOs;
using Newtonsoft.Json;
using RestSharp;
using Sacrra.Membership.Business.DTOs.DataWarehouseDTO;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Services.DataWarehouseService;
using System.Text.RegularExpressions;

namespace Sacrra.Membership.Business.Services.LookupsService
{
    public class LookupsService
    {
        public IMapper _mapper { get; }
        private LookupsServiceHelper _lookupsServiceHelper;
        private readonly AppDbContext _dbContext;
        private readonly DataWarehouseService.DataWarehouseService _dataWarehouseService;

        public LookupsService(IMapper mapper, LookupsServiceHelper lookupsServiceHelper, AppDbContext dbContext, DataWarehouseService.DataWarehouseService dataWarehouseService)
        {
            _mapper = mapper;
            _lookupsServiceHelper = lookupsServiceHelper;
            _dbContext = dbContext;
            _dataWarehouseService = dataWarehouseService;
        }

        public List<IdValuePairResource> GetEnumIdValuePairs<T>()
        {
            return _lookupsServiceHelper.GetEnumIdValuePairs<T>();
        }

        public List<IdValuePairResource> GetMembershipTypesForALGClientUpdateTask<T>(ClaimsPrincipal user)
        {
            var types = _lookupsServiceHelper.GetEnumIdValuePairs<T>();

            return types.Where(i => i.Value == "ALG Client").ToList();
        }

        public List<IdValuePairResource> GetMembershipTypesForMemberUpdateTask<T>(ClaimsPrincipal user)
        {
            var types = _lookupsServiceHelper.GetEnumIdValuePairs<T>();

            return types.Where(i => i.Value == "Full Member" || i.Value == "Non Member").ToList();
        }
        public List<IdValuePairResource> GetMembershipTypesById<T>(MembershipTypes membershipTypeId)
        {
            var types = _lookupsServiceHelper.GetEnumIdValuePairs<T>();

            switch (membershipTypeId)
            {
                case MembershipTypes.Bureau:
                    return types.Where(i => i.Value == "Bureau").ToList();
                case MembershipTypes.ALGLeader:
                    return types.Where(i => i.Value == "ALG Leader").ToList();
                case MembershipTypes.FullMember:
                case MembershipTypes.NonMember:
                    return types.Where(i => i.Value == "Full Member" || i.Value == "Non Member").ToList();
                case MembershipTypes.ALGClient:
                    return types.Where(i => i.Value == "ALG Client").ToList();
                case MembershipTypes.Affiliate:
                    return types.Where(i => i.Value == "Affiliate").ToList();
                default:
                    return new List<IdValuePairResource>();
            }
        }

        public List<IdValuePairResource> GetOSLAReasons()
        {
            var oslaReasons = _dbContext.Set<MonthlyOSLAReason>().ToList();
            var itemsToReturn = _mapper.Map<List<IdValuePairResource>>(oslaReasons);

            return itemsToReturn;
        }

        public List<ReplacementFileSubmissionCategoryOutputDTO> GetFileSubmissionCategories()
        {
            var data = _dbContext.Set<ReplacementFileSubmissionCategory>()
                .Include(i => i.Reasons)
                .ToList();

            var resource = _mapper.Map<List<ReplacementFileSubmissionCategoryOutputDTO>>(data);

            return resource;
        }

        public List<IdValuePairResource> GetReplacementFileSubmissionCategories()
        {
            var fileSubmissionCategoryList = _dbContext.Set<ReplacementFileSubmissionCategory>().ToList();
            var mappedFileSubmissionCategoryList = _mapper.Map<List<IdValuePairResource>>(fileSubmissionCategoryList);

            return mappedFileSubmissionCategoryList;
        }

        public List<IdValuePairResource> GetFileSubmissionReasons()
        {
            var data = _dbContext.Set<ReplacementFileSubmissionReason>().ToList();
            var resource = _mapper.Map<List<IdValuePairResource>>(data);

            return resource;
        }

        public async Task<List<IdValuePairResource>> GetSRNNumbersForCurrentUser(ClaimsPrincipal claimsPrincipal)
        {
            try
            {
                var currentUser = Helpers.Helpers.GetLoggedOnUser(_dbContext, claimsPrincipal);

                if (currentUser.RoleId == UserRoles.StakeHolderManager)
                {
                    return _dbContext.SRNs
                        .Where(x => x.SRNStatusId == 4)
                        .Select(x => new IdValuePairResource() { Id = x.Id, Value = x.SRNNumber })
                        .ToList();
                }                
                else if (currentUser.RoleId == UserRoles.ALGLeader)
                {
                    return _dbContext.SRNs
                        .Join(_dbContext.Members,
                            srn => srn.ALGLeaderId,
                            member => member.Id,
                            (srn, member) => new { srn, member })
                        .Join(_dbContext.MemberUsers,
                            x => x.member.Id,
                            memberUser => memberUser.MemberId,
                            (x, memberUser) => new { x.srn, x.member, memberUser })
                        .Where(x => x.memberUser.UserId == currentUser.Id
                            && x.member.MembershipTypeId == MembershipTypes.ALGLeader
                            && x.srn.SRNStatusId == 4)
                        .Select(x => new IdValuePairResource()
                        {
                            Id = x.srn.Id,
                            Value = x.srn.SRNNumber
                        })
                        .ToList();
                }
                else
                {
                    return _dbContext.SRNs
                        .Join(_dbContext.MemberUsers,
                            srn => srn.MemberId,
                            memberUser => memberUser.MemberId,
                            (srn, memberUser) => new { srn, memberUser })
                        .Where(x => x.memberUser.UserId == currentUser.Id && x.srn.SRNStatusId == 4)
                        .Select(x => new IdValuePairResource() { Id = x.srn.Id, Value = x.srn.SRNNumber })
                        .ToList();
                }                               
            }
            catch (Exception ex)
            {
                throw new LookupGetSRNNumbersException(0, "", ex.StackTrace);
            }
        }

        public List<IdValuePairResource> GetAdhocFileNames(string fileName = null)
        {
            try
            {
                if (string.IsNullOrEmpty(fileName))
                {
                    return new List<IdValuePairResource>();
                }
                // Updated regex to match the new file format
                var regex = new Regex(@"^(.{6}_(?:ALL|COMPUS|EXPERI|TRANSU|XDS|CPB|ITC|VCCB)_L702_(?:A)_\d{4}(?:0?[1-9]|1[012])(?:0?[1-9]|[12][0-9]|3[01])_)(\d+)(_\d+)(?:\.TXT\.PGP|\.ZIP\.PGP|\.RAR\.PGP|\.XLS\.PGP|\.XLSX\.PGP)$", RegexOptions.IgnoreCase);
                var match = regex.Match(fileName);
                if (!match.Success)
                {
                    return new List<IdValuePairResource>
                    {
                        new IdValuePairResource { Id = 0, Value = "Invalid filename format." }
                    };
                }
                var filePrefix = match.Groups[1].Value; // Everything up to the first number
                var firstNumber = match.Groups[2].Value; // First number after date (keep unchanged)
                var secondNumberStr = match.Groups[3].Value.Substring(1); // Second number (remove the "_")
                var fileExtension = fileName.Substring(fileName.LastIndexOf('.', fileName.LastIndexOf('.') - 1)); // Get the full extension (.TXT.PGP, .ZIP.PGP, etc.)

                // Determine the padding length from the original second number
                int originalPadding = secondNumberStr.Length;

                var query = _dbContext.AdhocFileSubmissions.AsQueryable();
                var exactExists = query.Any(x => x.FileName == fileName);
                // Find all files with the same prefix pattern including the first number (only vary the second number)
                var searchPrefix = $"{filePrefix}{firstNumber}_";
                var matchingFiles = query
                    .Where(x => x.FileName.StartsWith(searchPrefix))
                    .Select(x => x.FileName)
                    .ToList();
                int maxVersion = 0;
                foreach (var item in matchingFiles)
                {
                    var itemMatch = regex.Match(item);
                    if (itemMatch.Success && itemMatch.Groups[2].Value == firstNumber) // Ensure first number matches
                    {
                        var secondNumStr = itemMatch.Groups[3].Value.Substring(1); // Remove "_"
                        if (int.TryParse(secondNumStr, out int version))
                        {
                            if (version > maxVersion)
                            {
                                maxVersion = version;
                            }
                        }
                    }
                }

                // Always suggest the next version after the highest existing one
                // If no existing versions found (maxVersion == 0), suggest version 1
                var nextVersion = (maxVersion + 1).ToString().PadLeft(originalPadding, '0');
                var suggestedFileName = $"{filePrefix}{firstNumber}_{nextVersion}{fileExtension}";

                if (exactExists)
                {
                    return new List<IdValuePairResource>
                    {
                        new IdValuePairResource
                        {
                            Id = 0,
                            Value = $"{suggestedFileName}"
                        }
                    };
                }
                // If no matches in DB, check DataWarehouse
                if (matchingFiles.Count == 0)
                {
                    var dataWarehouseResults = SearchDataWarehouse(fileName);
                    return dataWarehouseResults;
                }
                return new List<IdValuePairResource>
                {
                    new IdValuePairResource
                    {
                        Id = 0,
                        Value = $"{suggestedFileName}"
                    }
                };
            }
            catch (Exception ex)
            {
                throw new CustomApiException(0, ex.Message, ex.StackTrace);
            }
        }


        private List<IdValuePairResource> SearchDataWarehouse(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
            {
                return new List<IdValuePairResource>();
            }
            // Updated regex to match the new file format
            var regex = new Regex(@"^(.{6}_(?:ALL|COMPUS|EXPERI|TRANSU|XDS|CPB|ITC|VCCB)_L702_(?:A)_\d{4}(?:0?[1-9]|1[012])(?:0?[1-9]|[12][0-9]|3[01])_)(\d+)(_\d+)(?:\.TXT\.PGP|\.ZIP\.PGP|\.RAR\.PGP|\.XLS\.PGP|\.XLSX\.PGP)$", RegexOptions.IgnoreCase);
            var match = regex.Match(fileName);
            if (!match.Success)
            {
                return new List<IdValuePairResource>
                {
                    new IdValuePairResource { Id = 0, Value = "Invalid filename format." }
                };
            }
            var filePrefix = match.Groups[1].Value; // Everything up to the first number
            var firstNumber = match.Groups[2].Value; // First number after date
            var secondNumberStr = match.Groups[3].Value.Substring(1); // Second number (remove the "_")
            var fileExtension = fileName.Substring(fileName.LastIndexOf('.', fileName.LastIndexOf('.') - 1)); // Get the full extension (.TXT.PGP, .ZIP.PGP, etc.)

            // Determine the padding length from the original second number
            int originalPadding = secondNumberStr.Length;

            // 1. Check for exact match
            var exactWhereClause = $"1 = 1 AND FileName IS NOT NULL AND FileName != '' AND FileName = '{fileName.Replace("'", "''")}'";
            var exactResults = QueryDataWarehouse(exactWhereClause);
            // 2. Get all files with the same prefix and first number (since the second number is the version)
            var prefixWhereClause = $"1 = 1 AND FileName IS NOT NULL AND FileName LIKE '{filePrefix.Replace("'", "''")}{firstNumber}_%'";
            var matchingResults = QueryDataWarehouse(prefixWhereClause);
            int maxVersion = 0;
            foreach (var result in matchingResults)
            {
                var itemMatch = regex.Match(result.Value);
                if (itemMatch.Success &&
                    itemMatch.Groups[2].Value == firstNumber && // Ensure same first number
                    int.TryParse(itemMatch.Groups[3].Value.Substring(1), out int version)) // Parse second number as version
                {
                    if (version > maxVersion)
                    {
                        maxVersion = version;
                    }
                }
            }
            // Always suggest the next version after the highest existing one
            // If no existing versions found (maxVersion == 0), suggest version 1
            var suggestedVersionNumber = maxVersion + 1;

            // Pad the suggested version number to match the original padding
            var suggestedVersion = suggestedVersionNumber.ToString().PadLeft(originalPadding, '0');
            var suggestedFileName = $"{filePrefix}{firstNumber}_{suggestedVersion}{fileExtension}";

            if (exactResults.Any())
            {
                return new List<IdValuePairResource>
                {
                    new IdValuePairResource
                    {
                        Id = 0,
                        Value = $"{suggestedFileName}"
                    }
                };
            }
            // If no exact match but prefix matches exist
            if (matchingResults.Any())
            {
                return new List<IdValuePairResource>
                {
                    new IdValuePairResource
                    {
                        Id = 0,
                        Value = $"{suggestedFileName}"
                    }
                };
            }
            // Completely new filename, no matches at all
            return new List<IdValuePairResource>
            {
                new IdValuePairResource
                {
                    Id = 0,
                    Value = $"{suggestedFileName}"
                }
            };
        }


        private List<IdValuePairResource> QueryDataWarehouse(string whereClause)
        {
            var apiCallModel = new DataWarehouseAPIModel()
            {
                Columns = "FileName",
                Where = whereClause
            };

            var fileNameList = new List<FileSubmissionOutputDTO>();
            try
            {
                fileNameList = _dataWarehouseService
                    .GetResultArray<FileSubmissionOutputDTO>("API.vwDailyAndMonthlyFileSubmissions", apiCallModel)
                    .ToList();
            }
            catch (Exception e)
            {
                // Log the exception but continue processing
                Console.WriteLine(e);
                return new List<IdValuePairResource>();
            }

            var dataWarehouseResults = new List<IdValuePairResource>();
            if (fileNameList.Any())
            {
                // Create results from data warehouse data                   
                for (int i = 0; i < fileNameList.Count; i++)
                {
                    dataWarehouseResults.Add(new IdValuePairResource()
                    {
                        Id = i + 1, // or another appropriate Id strategy
                        Value = fileNameList[i].FileName
                    });
                }
            }

            return dataWarehouseResults;
        }       

        public async Task<List<IdValuePairResource>> GetSPNumbersForCurrentUser(ClaimsPrincipal claimsPrincipal)
        {
            try
            {
                var currentUser = Helpers.Helpers.GetLoggedOnUser(_dbContext, claimsPrincipal);

                if (currentUser.RoleId == UserRoles.StakeHolderManager)
                {
                    return _dbContext.SPGroups
                        .Select(x => new IdValuePairResource() { Id = x.Id, Value = x.SPNumber })
                        .ToList();
                }
                else
                {
                    var memberIdList = _dbContext.MemberUsers.Where(x => x.UserId == currentUser.Id)
                        .Select(x => x.MemberId)
                        .ToList();
                    var memberSPNumberList = new List<IdValuePairResource>();

                    foreach (var memberId in memberIdList)
                    {
                        memberSPNumberList.AddRange(_dbContext.SPGroups
                            .Where(x => x.MemberId == memberId)
                            .Select(x => new IdValuePairResource() { Id = x.Id, Value = x.SPNumber })
                            .ToList());
                    }

                    return memberSPNumberList;
                }
            }
            catch (Exception ex)
            {
                throw new LookupGetSPNumbersException(0, "", ex.StackTrace);
            }
        }

        public async Task<List<IdValuePairResource>> GetSRNDisplayNamesForCurrentUser(ClaimsPrincipal claimsPrinciple)
        {
            var currentUser = Helpers.Helpers.GetLoggedOnUser(_dbContext, claimsPrinciple);

            if (currentUser.RoleId == UserRoles.StakeHolderManager)
            {
                return _dbContext.SRNs
                    .Where(x => x.SRNStatusId == 4)
                    .Select(x => new IdValuePairResource() { Id = x.Id, Value = x.TradingName })
                    .ToList();
            }
            else
            {
                var memberIdList = _dbContext.MemberUsers.Where(x => x.UserId == currentUser.Id)
                    .Select(x => x.MemberId)
                    .ToList();
                var memberSrnDisplayNameList = new List<IdValuePairResource>();

                foreach (var memberId in memberIdList)
                {
                    memberSrnDisplayNameList.AddRange(_dbContext.SRNs
                        .Where(x => x.MemberId == memberId)
                        .Where(x => x.SRNStatusId == 4)
                        .Select(x => new IdValuePairResource() { Id = x.Id, Value = x.TradingName })
                        .ToList());
                }

                return memberSrnDisplayNameList;
            }
        }

        public List<IdValuePairResource> GetReplacementFileSubmissionReasons()
        {
            var replacementFileReasonList = _dbContext.ReplacementFileSubmissionReasons.ToList();
            var mappedReplacementFileReasonList = _mapper.Map<List<IdValuePairResource>>(replacementFileReasonList);

            return mappedReplacementFileReasonList;
        }

        public List<IdValuePairResource> GetBureauUnsuccessfulAdhocLoadReasons()
        {
            var replacementFileReasonList = _dbContext.AdhocFileSubmissionReason.ToList();
            var mappedReplacementFileReasonList = _mapper.Map<List<IdValuePairResource>>(replacementFileReasonList);

            return mappedReplacementFileReasonList;
        }

        public List<IdValuePairResource> GetAdhocFileSubmissionReasons()
        {
            var adhocFileReasonList = _dbContext.AdhocFileSubmissionReason.ToList();
            var mappedAdhocFileReasonList = _mapper.Map<List<IdValuePairResource>>(adhocFileReasonList);

            return mappedAdhocFileReasonList;
        }


        public List<IdValuePairResource> GetUserMembers(int userId)
        {
            var userMemberIdList = _dbContext.MemberUsers.Where(x => x.UserId == userId)
                .Select(x => x.MemberId)
                .ToList();

            return _dbContext.Members
                .Where(x => userMemberIdList.Contains(x.Id))
                .Select(x => new IdValuePairResource() { Id = x.Id, Value = x.RegisteredName })
                .ToList();
        }

        public List<IdValuePairResource> GetAllStakeholderManagers()
        {
            return _dbContext.Users
                .Where(x => x.RoleId == UserRoles.StakeHolderManager)
                .Select(x => new IdValuePairResource() { Id = x.Id, Value = x.FullName })
                .ToList();
        }

        public List<IdValuePairResource> GetAllALGLeaders()
        {
            return _dbContext.Members
                .Where(x => x.MembershipTypeId == MembershipTypes.ALGLeader)
                .Select(x => new IdValuePairResource() { Id = x.Id, Value = x.RegisteredName })
                .ToList();
        }



        public List<IdValuePairResource> GetBureauUnsuccessfulAdHocLoadReasons()
        {
            var replacementFileReasonList = _dbContext.ReplacementFileSubmissionReasons.ToList();
            var mappedReplacementFileReasonList = _mapper.Map<List<IdValuePairResource>>(replacementFileReasonList);
            var item = mappedReplacementFileReasonList.Find(x => x.Value == "File failed DTH validation");

            mappedReplacementFileReasonList.Remove(item);

            return mappedReplacementFileReasonList;
        }

        public dynamic GetSRNFileStatuses(int srnId)
        {
            var mappedFileList = new List<dynamic>();

            var srn = _dbContext.SRNs
                .Where(searchSRN => searchSRN.Id == srnId)
                .FirstOrDefault();

            var filesForSRN = _dbContext.vwSRNWithUpdateHistories
                .Where(searchHistory => searchHistory.SRNId == srnId && searchHistory.IsLatestHistory == 1)
                .ToList();

            foreach (var file in filesForSRN)
            {
                // We do not want to consider the 0 file types
                // as we only work with 1 and 2 (Daily and Monthly)
                if (file.HistoryFileType == 0)
                {
                    continue;
                }

                mappedFileList.Add(new
                {
                    FileType = file.HistoryFileType,
                    FileStatus = file.HistoryStatusId
                });
            }

            return new
            {
                // If the files count is more than 2, it means there is a 0 file type,
                // which we want to ignore.
                NumberOfFiles = filesForSRN.Count > 2 ? 2 : filesForSRN.Count,
                Files = mappedFileList
            };
        }
    }
}