using AutoMapper;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.Services.LookupsService;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Freshdesk.DTOs;
using Sacrra.Membership.Freshdesk.DTOs.Ticket;
using Sacrra.Membership.Freshdesk.Enums;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Sacrra.Membership.Freshdesk.Services
{
    public class TicketsService
    {
        private readonly IConfiguration _configuration;
        private LookupsService _lookupsService;
        private readonly CompanyService _companyService;

        public TicketsService(IConfiguration configuration, LookupsService lookupsService, CompanyService companyService)
        {
            _configuration = configuration;
            _lookupsService = lookupsService;
            _companyService = companyService;
        }

        public TicketSummaryDTO GetTicketsSummary(TicketFilterInputDTO filter, int page = 1)
        {
            var contacts = GetContacts(page: 1);
            int count = 2;
            
            while (contacts?.Count() >= 100) /* contact endpoint returns max of 30 records, so we need to go through all pages */
            {
                AllContacts.AddRange(contacts);
                contacts = GetContacts(count);
                count++;
            }

            var ticketsSummary = new TicketSummaryDTO();
            int total = 0;

            var ticketsResult = GetTickets(filter, page);

            if (ticketsResult != null)
            {
                if (ticketsResult.Item2 != null)
                {
                    if (ticketsResult.Item2.Count > 0)
                    {
                        ticketsSummary.Tickets.AddRange(ticketsResult.Item2);
                        int counter = page;
                        total = ticketsResult.Item2.Count;

                        while (ticketsResult != null && ticketsResult.Item1 > total)
                        {
                            counter++;
                            ticketsResult = GetTickets(filter, counter);

                            if (ticketsResult != null)
                            {
                                if (ticketsResult.Item2 != null)
                                {
                                    if (ticketsResult.Item2.Count > 0)
                                    {
                                        ticketsSummary.Tickets.AddRange(ticketsResult.Item2);
                                        total += ticketsResult.Item2.Count;
                                    }
                                }
                            }
                        }

                        if (ticketsSummary.Tickets != null)
                        {
                            if (ticketsSummary.Tickets.Count > 0)
                            {
                                if (filter.Id > 0)
                                {
                                    ticketsSummary.Tickets = ticketsSummary.Tickets.Where(i => i.Id == filter.Id).ToList();
                                }

                                if (filter.ResolutionDate != null && filter.ResolutionDate != DateTime.MinValue)
                                {
                                    ticketsSummary.Tickets = ticketsSummary.Tickets.Where(i => i.ResolutionDate != null).ToList();

                                    if (ticketsSummary.Tickets != null)
                                    {

                                        ticketsSummary.Tickets = ticketsSummary.Tickets.Where(i => string.Format("{0:yyyy-MM-dd}", Convert.ToDateTime(i.ResolutionDate)) == string.Format("{0:yyyy-MM-dd}", filter.ResolutionDate)).ToList();
                                    }
                                }

                                if (!string.IsNullOrWhiteSpace(filter.RaisedBy))
                                {
                                    ticketsSummary.Tickets = ticketsSummary.Tickets.Where(i => i.RequestedBy != null).ToList();

                                    if(ticketsSummary.Tickets != null)
                                    {
                                        ticketsSummary.Tickets = ticketsSummary.Tickets.Where(i => i.RequestedBy.ToLower().Contains(filter.RaisedBy.ToLower())).ToList();
                                    }
                                }
                            }
                        }
                    }
                }
            }

            ticketsSummary.Total = total;

            return ticketsSummary;
        }

        private async Task<Tuple<int, List<TicketGetDTO>>> GetTickets(TicketFilterInputDTO filter, int page = 1)
        {
            try
            {
                var client = Helpers.GetRestClient(_configuration);
                string queryFilter = _configuration.GetSection("FreshdeskIntegration")["TicketQueryFilter"];

                if (filter != null)
                {
                    if (filter.AgentId > 0)
                    {
                        queryFilter += $" AND agent_id:{filter.AgentId}";
                    }
                    if (filter.StatusId > 0)
                    {
                        queryFilter += $" AND status:{filter.StatusId}";
                    }
                    if (!string.IsNullOrEmpty(filter.Type))
                    {
                        queryFilter += $" AND type:'{filter.Type}'";
                    }
                    if (filter.UpdatedAt != null && filter.UpdatedAt != DateTime.MinValue)
                    {
                        queryFilter += $" AND updated_at:'{string.Format("{0:yyyy-MM-dd}", filter.UpdatedAt)}'";
                    }
                    if ((filter.CreationDateFrom == null || filter.CreationDateFrom == DateTime.MinValue) && (filter.CreationDateTo == null || filter.CreationDateTo == DateTime.MinValue))
                    {
                        DateTime dateFrom = DateTime.Now.AddDays(-30).Date;
                        DateTime dateTo = DateTime.Now.Date;

                        queryFilter += $" AND created_at:>'{string.Format("{0:yyyy-MM-dd}", dateFrom)}'";
                        queryFilter += $" AND created_at:<'{string.Format("{0:yyyy-MM-dd}", dateTo)}'";
                    }
                    else if ((filter.CreationDateFrom != null && filter.CreationDateFrom != DateTime.MinValue) && (filter.CreationDateTo != null && filter.CreationDateTo != DateTime.MinValue))
                    {
                        queryFilter += $" AND created_at:>'{string.Format("{0:yyyy-MM-dd}", filter.CreationDateFrom)}'";
                        queryFilter += $" AND created_at:<'{string.Format("{0:yyyy-MM-dd}", filter.CreationDateTo)}'";
                    }
                }
                RestRequest request = new()
                {
                    Resource = $@"api/v2/search/tickets?query=""{queryFilter}""&page={page}",
                    Method = Method.Get
                };
                var response = client.ExecuteAsync(request);

                if (response.IsSuccessful)
                {
                    var data = JsonConvert.DeserializeObject<SearchTicketDTO>(response.Content);
                    var tickets = new List<TicketGetDTO>();

                    if (data != null)
                    {
                        if (data.Results != null)
                        {
                            var allAgents = GetAgents();
                            
                            var allStatuses = GetTicketStatuses();

                            foreach (var result in data.Results)
                            {
                                var ticket = new TicketGetDTO
                                {
                                    Id = result.Id,
                                    Type = result.Type,
                                    Subject = result.Subject,
                                    DueByDate = (result.DueBy != DateTime.MinValue)? string.Format("{0:yyyy-MM-dd hh:mm:ss}", result.DueBy) : "",
                                    ImpactedMember = result.CustomFields?.ImpactedMember
                                };

                                if(result.ResponderId > 0)
                                {
                                    ticket.Agent = allAgents?.Where(i => i.Id == result.ResponderId).Select(i => i.Value).FirstOrDefault();
                                }

                                var requestedBy = AllContacts?.Where(i => i.Id == result.RequesterId).Select(i => i.Name).FirstOrDefault();

                                //Sometimes we don't get the 'requestedBy' value if the ticket was create by an agent
                                //Therefore we need to also get the 'requestedBy' from the list of agents
                                if (requestedBy == null)
                                {
                                    var requester = allAgents?.Where(i => i.Id == result.RequesterId).Select(i => i.Value).FirstOrDefault();
                                    ticket.RequestedBy = requester;
                                }
                                else
                                {
                                    ticket.RequestedBy = requestedBy;
                                }

                                ticket.Status = allStatuses?.Where(i => i.Id == result.Status).Select(i => i.Value).FirstOrDefault();

                                PopulateTicketResolutionDate(ticket, result);

                                tickets.Add(ticket);
                            }
                        }

                        return new Tuple<int, List<TicketGetDTO>>(data.Total, tickets);
                    }
                }

                if (!string.IsNullOrWhiteSpace(queryFilter))
                {

                }

                return null;

            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Unable to get tickets with provided tag from Freshdesk");
                return null;
            }
        }

        public TicketRequesterDetailDTO GetRequester(string ticketId)
        {
            try
            {
                var client = Helpers.GetRestClient(_configuration);
                RestRequest request = new()
                {
                    Resource = $@"api/v2/tickets/{ticketId}?include=requester",
                    Method = Method.Get
                };

                var response = client.ExecuteAsync(request);

                if (response.IsSuccessful)
                {
                    var ticket = JsonConvert.DeserializeObject<TicketRequesterDetailDTO>(response.Content);

                    ticket.Description = ticket.Description.Replace("\"", "");
                    return ticket;
                }

                return null;

            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Unable to get ticket conversations with provided id from Freshdesk");
                return null;
            }
        }

        public ConversationsSummaryDTO GetAllTicketConversations(string ticketId, int page = 1)
        {
            try
            {
                var conversationSummary = new ConversationsSummaryDTO();

                var client = Helpers.GetRestClient(_configuration);
                RestRequest request = new()
                {
                    Resource = $@"api/v2/tickets/{ticketId}/conversations?page={page}",
                    Method = Method.Get
                };

                var response = client.ExecuteAsync(request);

                if (response.IsSuccessful)
                {
                    var conversations = JsonConvert.DeserializeObject<TicketGetConversationsDTO[]>(response.Content);

                    foreach (var conversation in conversations)
                    {
                        conversation.Body = conversation.Body
                            .Replace("\n", "")
                            .Replace("\"", "");
                    }

                    conversationSummary.Conversations.AddRange(conversations);

                    if (conversations.Length >= 30 /* max number of records per page from Freshdesk endpoint */)
                    {
                        request.Resource = $@"api/v2/tickets/{ticketId}/conversations?page={page + 1}";
                        response = client.ExecuteAsync(request);

                        if (response.IsSuccessful)
                        {
                            conversations = JsonConvert.DeserializeObject<TicketGetConversationsDTO[]>(response.Content);
                            if (conversations.Length > 0)
                            {
                                conversationSummary.HasMoreConversations = true;
                            }
                        }
                    }

                    return conversationSummary;
                }

                return null;

            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Unable to get ticket conversations with provided id from Freshdesk");
                return null;
            }
        }

        public AgentGetDTO GetAgent(long id)
        {
            try
            {
                var client = Helpers.GetRestClient(_configuration);

                if (id > 0)
                {
                    RestRequest request = new()
                    {
                        Resource = $"/api/v2/agents/{id}",
                        Method = Method.Get
                    };
                    var response = client.ExecuteAsync(request);

                    if (response.IsSuccessful)
                    {
                        var data = JsonConvert.DeserializeObject<AgentGetDTO>(response.Content);

                        if (data != null)
                        {
                            return data;
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Unable to get agent with id {id} from Freshdesk");
                return null;
            }
        }

        public AgentGetDTO GetUser(long id)
        {
            try
            {
                var client = Helpers.GetRestClient(_configuration);

                if (id > 0)
                {
                    RestRequest request = new()
                    {
                        Resource = $"/api/v2/agents/{id}",
                        Method = Method.Get
                    };
                    var response = client.ExecuteAsync(request);

                    if (response.IsSuccessful)
                    {
                        var data = JsonConvert.DeserializeObject<AgentGetDTO>(response.Content);

                        if (data != null)
                        {
                            return data;
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Unable to get agent with id {id} from Freshdesk");
                return null;
            }
        }

        public ContactDTO GetContact(long id)
        {
            try
            {
                var client = Helpers.GetRestClient(_configuration);

                if (id > 0)
                {
                    RestRequest request = new()
                    {
                        Resource = $"/api/v2/contacts/{id}",
                        Method = Method.Get
                    };
                    var response = client.ExecuteAsync(request);

                    if (response.IsSuccessful)
                    {
                        var data = JsonConvert.DeserializeObject<ContactDTO>(response.Content);

                        if (data != null)
                        {
                            return data;
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Unable to get contact with id {id} from Freshdesk");
                return null;
            }
        }

        public async Task<IEnumerable<ContactDTO>> GetContacts(int page = 1)
        {
            try
            {
                var client = Helpers.GetRestClient(_configuration);

                RestRequest request = new()
                {
                    Resource = $"/api/v2/contacts?per_page=100&page={page}",
                    Method = Method.Get
                };
                var response = client.ExecuteAsync(request);

                if (response.IsSuccessful)
                {
                    var data = JsonConvert.DeserializeObject<IEnumerable<ContactDTO>>(response.Content);

                    if (data != null)
                    {
                        return data;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Unable to get contacts from Freshdesk");
                return null;
            }
        }

        public IEnumerable<IdValuePairResource> GetTicketStatuses()
        {
            return EnumHelper.GetEnumIdValuePairs<TicketStatusEnum>();
        }

        public string GetTicketStatus(int statusId)
        {
            if (statusId > 0)
            {
                var status = EnumHelper.GetEnumIdValuePair<TicketStatusEnum>(statusId);
                return status != null ? status.Value : null;
            }

            return null;
        }

        private void PopulateTicketDetails(SearchTicketItemDTO searchResults, TicketGetDTO ticket)
        {
            if (ticket != null && searchResults != null)
            {
                if(searchResults.ResponderId > 0)
                {
                    var agent = GetAgent((long)searchResults.ResponderId);
                    if (agent != null)
                    {
                        ticket.Agent = agent.Contact?.Name;
                    }
                }
                
                var requestedBy = GetContact(searchResults.RequesterId);
                //Sometimes we don't get the 'requestedBy' value if the ticket was create by an agent
                //Therefore we need to also get the 'requestedBy' from the list of agents
                if (requestedBy == null)
                {
                    var requester = GetAgent(searchResults.RequesterId);
                    ticket.RequestedBy = requester.Contact?.Name;
                }
                else
                {
                    ticket.RequestedBy = requestedBy.Name;
                }

                ticket.Status = GetTicketStatus(searchResults.Status);

                PopulateTicketResolutionDate(ticket, searchResults);
            }
        }

        private void PopulateTicketResolutionDate(TicketGetDTO ticket, SearchTicketItemDTO searchResults)
        {
            if (searchResults != null && ticket != null)
            {
                //If status = "Resolved" or status = "Closed"
                if (searchResults.Status == 4 || searchResults.Status == 5)
                {
                    ticket.ResolutionDate = string.Format("{0:yyyy-MM-dd hh:mm:ss}", searchResults.UpdatedAt);
                }
                else
                {
                    ticket.ResolutionDate = null;
                }

                ticket.UpdatedAt = string.Format("{0:yyyy-MM-dd hh:mm:ss}", searchResults.UpdatedAt);
            }
        }

        public async Task<IEnumerable<IdValuePairDTO>> GetAgents()
        {
            try
            {
                var client = Helpers.GetRestClient(_configuration);

                RestRequest request = new()
                {
                    Resource = $"/api/v2/agents",
                    Method = Method.Get
                };
                var response = client.ExecuteAsync(request);

                if (response.IsSuccessful)
                {
                    var data = JsonConvert.DeserializeObject<IEnumerable<AgentGetDTO>>(response.Content);

                    if (data != null)
                    {
                        var agents = data
                            .Select(x => new IdValuePairDTO
                            {
                                Id = x.Id,
                                Value = x.Contact?.Name
                            })
                            .ToList();

                        if (agents != null)
                        {
                            return agents;
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Unable to get agents from Freshdesk");
                return null;
            }
        }

        public async Task<IEnumerable<IdValuePairDTO>> GetTicketField(long id)
        {
            try
            {
                var client = Helpers.GetRestClient(_configuration);

                RestRequest request = new()
                {
                    Resource = $"/api/v2/admin/ticket_fields/{id}",
                    Method = Method.Get
                };
                var response = client.ExecuteAsync(request);

                if (response.IsSuccessful)
                {
                    var data = JsonConvert.DeserializeObject<TicketFieldGetDTO>(response.Content);

                    if (data != null)
                    {
                        if (data.Choices != null)
                        {
                            return data.Choices;
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Unable to get ticket field choices for ticket id {id} from Freshdesk");
                return null;
            }
        }

        public TicketFilterGetAllDTO GetAllTicketFilters(long fieldId)
        {
            var types = GetTicketField(fieldId);

            var filters = new TicketFilterGetAllDTO
            {
                Agents = GetAgents(),
                Statuses = _lookupsService.GetEnumIdValuePairs<TicketStatusEnum>(),
                Types = types
            };

            return filters;
        }

        public SearchTicketItemDTO AutoPopulateImpactedMemberField(string apiKey, AutoPopulateImpactedMemberInputDTO inputDTO)
        {
            if (IsValidWebhookApiKey(apiKey))
            {
                if (inputDTO != null)
                {
                    if (inputDTO.TicketId > 0)
                    {
                        try
                        {
                            var client = Helpers.GetRestClient(_configuration);

                            RestRequest request = new()
                            {
                                Resource = $"/api/v2/tickets/{inputDTO.TicketId}",
                                Method = Method.Put
                            };

                            var updateTicket = new
                            {
                                custom_fields = new
                                {
                                    cf_impacted_member = inputDTO.CompanyName
                                },
                            };

                            request.AddJsonBody(updateTicket);

                            var response = client.ExecuteAsync(request);

                            if (response.IsSuccessful)
                            {
                                var data = JsonConvert.DeserializeObject<SearchTicketItemDTO>(response.Content);

                                return data;
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.Error(ex, $"Unable to update ticket's impacted member field from Freshdesk. Ticket with id {inputDTO.TicketId}");
                        }
                    }
                }
            }
            
            
            return null;
        }

        public bool IsValidWebhookApiKey(string apiKey)
        {
            if (string.IsNullOrEmpty(apiKey))
            {
                return false;
            }

            var webhookApiKey = _configuration.GetSection("FreshdeskIntegration:Webhook")["APIKey"];

            if (webhookApiKey == apiKey)
            {
                return true;
            }

            return false;
        }

        public void BulkPopulateImpactedMemberField(TicketFilterInputDTO filter, int page = 1)
        {
            try
            {
                var tickets = GetAllWebhookTickets(filter, page);

                if (tickets != null)
                {
                    if(tickets.Results != null)
                    {
                        if(tickets.Total > tickets.Results.Count())
                        {
                            UpdateImpactedMemberField(tickets.Results);

                            while (tickets.Total > tickets.Results.Count())
                            {
                                page++;
                                tickets = GetAllWebhookTickets(filter, page);

                                UpdateImpactedMemberField(tickets.Results);
                            }
                        }
                        else
                        {
                            UpdateImpactedMemberField(tickets.Results);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Unable to bulk update ticket's impacted member field from Freshdesk");
            }
        }

        private SearchTicketDTO GetAllWebhookTickets(TicketFilterInputDTO filter, int page = 1)
        {

            try
            {
                var client = Helpers.GetRestClient(_configuration);
                string queryFilter = _configuration.GetSection("FreshdeskIntegration:Webhook")["TicketQueryFilter"];

                if (filter != null)
                {
                    if (filter.AgentId > 0)
                    {
                        queryFilter += $" AND agent_id:{filter.AgentId}";
                    }
                    if (filter.StatusId > 0)
                    {
                        queryFilter += $" AND status:{filter.StatusId}";
                    }
                    if (!string.IsNullOrEmpty(filter.Type))
                    {
                        queryFilter += $" AND type:'{filter.Type}'";
                    }
                    if (filter.UpdatedAt != null && filter.UpdatedAt != DateTime.MinValue)
                    {
                        queryFilter += $" AND updated_at:'{string.Format("{0:yyyy-MM-dd}", filter.UpdatedAt)}'";
                    }
                    else if ((filter.CreationDateFrom != null && filter.CreationDateFrom != DateTime.MinValue) && (filter.CreationDateTo != null && filter.CreationDateTo != DateTime.MinValue))
                    {
                        queryFilter += $" AND created_at:>'{string.Format("{0:yyyy-MM-dd}", filter.CreationDateFrom)}'";
                        queryFilter += $" AND created_at:<'{string.Format("{0:yyyy-MM-dd}", filter.CreationDateTo)}'";
                    }
                }
                RestRequest request = new()
                {
                    Resource = $@"api/v2/search/tickets?query=""{queryFilter}""&page={page}",
                    Method = Method.Get
                };
                var response = client.ExecuteAsync(request);

                if (response.IsSuccessful)
                {
                    var data = JsonConvert.DeserializeObject<SearchTicketDTO>(response.Content);

                    return data;
                }

                return null;

            }
            catch (Exception ex)
            {
                Log.Error(ex, $"Unable to get tickets with provided tag from Freshdesk");
                return null;
            }
        }

        private void UpdateImpactedMemberField(IEnumerable<SearchTicketItemDTO> tickets)
        {
            var client = Helpers.GetRestClient(_configuration);

            foreach (var ticket in tickets)
            {
                if (ticket.CompanyId > 0)
                {
                    var company = _companyService.GetCompanyById((long)ticket.CompanyId);

                    if (company != null)
                    {
                        RestRequest request = new()
                        {
                            Resource = $"/api/v2/tickets/{ticket.Id}",
                            Method = Method.Put
                        };

                        var updateTicket = new
                        {
                            custom_fields = new
                            {
                                cf_impacted_member = company.Name
                            },
                        };

                        request.AddJsonBody(updateTicket);

                        var response = client.ExecuteAsync(request);
                    }
                }
            }
        }

        public List<ContactDTO> AllContacts { get; set; } = new List<ContactDTO>();
    }
}
